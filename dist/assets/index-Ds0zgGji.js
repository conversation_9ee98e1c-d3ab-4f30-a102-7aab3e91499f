var kc=e=>{throw TypeError(e)};var yl=(e,t,n)=>t.has(e)||kc("Cannot "+n);var k=(e,t,n)=>(yl(e,t,"read from private field"),n?n.call(e):t.get(e)),Z=(e,t,n)=>t.has(e)?kc("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),U=(e,t,n,r)=>(yl(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),ke=(e,t,n)=>(yl(e,t,"access private method"),n);var xs=(e,t,n,r)=>({set _(o){U(e,t,o,n)},get _(){return k(e,t,r)}});function Og(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function jf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Tf={exports:{}},Fi={},Rf={exports:{}},Y={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var as=Symbol.for("react.element"),_g=Symbol.for("react.portal"),Lg=Symbol.for("react.fragment"),Ig=Symbol.for("react.strict_mode"),Dg=Symbol.for("react.profiler"),zg=Symbol.for("react.provider"),Fg=Symbol.for("react.context"),$g=Symbol.for("react.forward_ref"),Bg=Symbol.for("react.suspense"),Ug=Symbol.for("react.memo"),Vg=Symbol.for("react.lazy"),Pc=Symbol.iterator;function Wg(e){return e===null||typeof e!="object"?null:(e=Pc&&e[Pc]||e["@@iterator"],typeof e=="function"?e:null)}var Af={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Mf=Object.assign,Of={};function io(e,t,n){this.props=e,this.context=t,this.refs=Of,this.updater=n||Af}io.prototype.isReactComponent={};io.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};io.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function _f(){}_f.prototype=io.prototype;function iu(e,t,n){this.props=e,this.context=t,this.refs=Of,this.updater=n||Af}var lu=iu.prototype=new _f;lu.constructor=iu;Mf(lu,io.prototype);lu.isPureReactComponent=!0;var jc=Array.isArray,Lf=Object.prototype.hasOwnProperty,au={current:null},If={key:!0,ref:!0,__self:!0,__source:!0};function Df(e,t,n){var r,o={},s=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(s=""+t.key),t)Lf.call(t,r)&&!If.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:as,type:e,key:s,ref:i,props:o,_owner:au.current}}function Hg(e,t){return{$$typeof:as,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function uu(e){return typeof e=="object"&&e!==null&&e.$$typeof===as}function Qg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Tc=/\/+/g;function xl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Qg(""+e.key):t.toString(36)}function Us(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case as:case _g:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+xl(i,0):r,jc(o)?(n="",e!=null&&(n=e.replace(Tc,"$&/")+"/"),Us(o,t,n,"",function(u){return u})):o!=null&&(uu(o)&&(o=Hg(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(Tc,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",jc(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+xl(s,l);i+=Us(s,t,n,a,o)}else if(a=Wg(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+xl(s,l++),i+=Us(s,t,n,a,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function ws(e,t,n){if(e==null)return e;var r=[],o=0;return Us(e,r,"","",function(s){return t.call(n,s,o++)}),r}function Kg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Fe={current:null},Vs={transition:null},Yg={ReactCurrentDispatcher:Fe,ReactCurrentBatchConfig:Vs,ReactCurrentOwner:au};function zf(){throw Error("act(...) is not supported in production builds of React.")}Y.Children={map:ws,forEach:function(e,t,n){ws(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ws(e,function(){t++}),t},toArray:function(e){return ws(e,function(t){return t})||[]},only:function(e){if(!uu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Y.Component=io;Y.Fragment=Lg;Y.Profiler=Dg;Y.PureComponent=iu;Y.StrictMode=Ig;Y.Suspense=Bg;Y.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Yg;Y.act=zf;Y.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Mf({},e.props),o=e.key,s=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,i=au.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Lf.call(t,a)&&!If.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:as,type:e.type,key:o,ref:s,props:r,_owner:i}};Y.createContext=function(e){return e={$$typeof:Fg,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:zg,_context:e},e.Consumer=e};Y.createElement=Df;Y.createFactory=function(e){var t=Df.bind(null,e);return t.type=e,t};Y.createRef=function(){return{current:null}};Y.forwardRef=function(e){return{$$typeof:$g,render:e}};Y.isValidElement=uu;Y.lazy=function(e){return{$$typeof:Vg,_payload:{_status:-1,_result:e},_init:Kg}};Y.memo=function(e,t){return{$$typeof:Ug,type:e,compare:t===void 0?null:t}};Y.startTransition=function(e){var t=Vs.transition;Vs.transition={};try{e()}finally{Vs.transition=t}};Y.unstable_act=zf;Y.useCallback=function(e,t){return Fe.current.useCallback(e,t)};Y.useContext=function(e){return Fe.current.useContext(e)};Y.useDebugValue=function(){};Y.useDeferredValue=function(e){return Fe.current.useDeferredValue(e)};Y.useEffect=function(e,t){return Fe.current.useEffect(e,t)};Y.useId=function(){return Fe.current.useId()};Y.useImperativeHandle=function(e,t,n){return Fe.current.useImperativeHandle(e,t,n)};Y.useInsertionEffect=function(e,t){return Fe.current.useInsertionEffect(e,t)};Y.useLayoutEffect=function(e,t){return Fe.current.useLayoutEffect(e,t)};Y.useMemo=function(e,t){return Fe.current.useMemo(e,t)};Y.useReducer=function(e,t,n){return Fe.current.useReducer(e,t,n)};Y.useRef=function(e){return Fe.current.useRef(e)};Y.useState=function(e){return Fe.current.useState(e)};Y.useSyncExternalStore=function(e,t,n){return Fe.current.useSyncExternalStore(e,t,n)};Y.useTransition=function(){return Fe.current.useTransition()};Y.version="18.3.1";Rf.exports=Y;var w=Rf.exports;const A=jf(w),Ff=Og({__proto__:null,default:A},[w]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gg=w,qg=Symbol.for("react.element"),Xg=Symbol.for("react.fragment"),Zg=Object.prototype.hasOwnProperty,Jg=Gg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ev={key:!0,ref:!0,__self:!0,__source:!0};function $f(e,t,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)Zg.call(t,r)&&!ev.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:qg,type:e,key:s,ref:i,props:o,_owner:Jg.current}}Fi.Fragment=Xg;Fi.jsx=$f;Fi.jsxs=$f;Tf.exports=Fi;var c=Tf.exports,Bf={exports:{}},tt={},Uf={exports:{}},Vf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,j){var L=P.length;P.push(j);e:for(;0<L;){var W=L-1>>>1,z=P[W];if(0<o(z,j))P[W]=j,P[L]=z,L=W;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var j=P[0],L=P.pop();if(L!==j){P[0]=L;e:for(var W=0,z=P.length,K=z>>>1;W<K;){var q=2*(W+1)-1,me=P[q],Ne=q+1,J=P[Ne];if(0>o(me,L))Ne<z&&0>o(J,me)?(P[W]=J,P[Ne]=L,W=Ne):(P[W]=me,P[q]=L,W=q);else if(Ne<z&&0>o(J,L))P[W]=J,P[Ne]=L,W=Ne;else break e}}return j}function o(P,j){var L=P.sortIndex-j.sortIndex;return L!==0?L:P.id-j.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var i=Date,l=i.now();e.unstable_now=function(){return i.now()-l}}var a=[],u=[],d=1,p=null,h=3,f=!1,b=!1,y=!1,x=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(P){for(var j=n(u);j!==null;){if(j.callback===null)r(u);else if(j.startTime<=P)r(u),j.sortIndex=j.expirationTime,t(a,j);else break;j=n(u)}}function S(P){if(y=!1,v(P),!b)if(n(a)!==null)b=!0,$(E);else{var j=n(u);j!==null&&V(S,j.startTime-P)}}function E(P,j){b=!1,y&&(y=!1,g(T),T=-1),f=!0;var L=h;try{for(v(j),p=n(a);p!==null&&(!(p.expirationTime>j)||P&&!F());){var W=p.callback;if(typeof W=="function"){p.callback=null,h=p.priorityLevel;var z=W(p.expirationTime<=j);j=e.unstable_now(),typeof z=="function"?p.callback=z:p===n(a)&&r(a),v(j)}else r(a);p=n(a)}if(p!==null)var K=!0;else{var q=n(u);q!==null&&V(S,q.startTime-j),K=!1}return K}finally{p=null,h=L,f=!1}}var C=!1,N=null,T=-1,O=5,M=-1;function F(){return!(e.unstable_now()-M<O)}function D(){if(N!==null){var P=e.unstable_now();M=P;var j=!0;try{j=N(!0,P)}finally{j?Q():(C=!1,N=null)}}else C=!1}var Q;if(typeof m=="function")Q=function(){m(D)};else if(typeof MessageChannel<"u"){var _=new MessageChannel,G=_.port2;_.port1.onmessage=D,Q=function(){G.postMessage(null)}}else Q=function(){x(D,0)};function $(P){N=P,C||(C=!0,Q())}function V(P,j){T=x(function(){P(e.unstable_now())},j)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){b||f||(b=!0,$(E))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(P){switch(h){case 1:case 2:case 3:var j=3;break;default:j=h}var L=h;h=j;try{return P()}finally{h=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,j){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var L=h;h=P;try{return j()}finally{h=L}},e.unstable_scheduleCallback=function(P,j,L){var W=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?W+L:W):L=W,P){case 1:var z=-1;break;case 2:z=250;break;case 5:z=**********;break;case 4:z=1e4;break;default:z=5e3}return z=L+z,P={id:d++,callback:j,priorityLevel:P,startTime:L,expirationTime:z,sortIndex:-1},L>W?(P.sortIndex=L,t(u,P),n(a)===null&&P===n(u)&&(y?(g(T),T=-1):y=!0,V(S,L-W))):(P.sortIndex=z,t(a,P),b||f||(b=!0,$(E))),P},e.unstable_shouldYield=F,e.unstable_wrapCallback=function(P){var j=h;return function(){var L=h;h=j;try{return P.apply(this,arguments)}finally{h=L}}}})(Vf);Uf.exports=Vf;var tv=Uf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nv=w,et=tv;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Wf=new Set,Fo={};function pr(e,t){Xr(e,t),Xr(e+"Capture",t)}function Xr(e,t){for(Fo[e]=t,e=0;e<t.length;e++)Wf.add(t[e])}var Xt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Zl=Object.prototype.hasOwnProperty,rv=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Rc={},Ac={};function ov(e){return Zl.call(Ac,e)?!0:Zl.call(Rc,e)?!1:rv.test(e)?Ac[e]=!0:(Rc[e]=!0,!1)}function sv(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function iv(e,t,n,r){if(t===null||typeof t>"u"||sv(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function $e(e,t,n,r,o,s,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=i}var Ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ce[e]=new $e(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ce[t]=new $e(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ce[e]=new $e(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ce[e]=new $e(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ce[e]=new $e(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ce[e]=new $e(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ce[e]=new $e(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ce[e]=new $e(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ce[e]=new $e(e,5,!1,e.toLowerCase(),null,!1,!1)});var cu=/[\-:]([a-z])/g;function du(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(cu,du);Ce[t]=new $e(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(cu,du);Ce[t]=new $e(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(cu,du);Ce[t]=new $e(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ce[e]=new $e(e,1,!1,e.toLowerCase(),null,!1,!1)});Ce.xlinkHref=new $e("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ce[e]=new $e(e,1,!1,e.toLowerCase(),null,!0,!0)});function fu(e,t,n,r){var o=Ce.hasOwnProperty(t)?Ce[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(iv(t,n,o,r)&&(n=null),r||o===null?ov(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var rn=nv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,bs=Symbol.for("react.element"),Sr=Symbol.for("react.portal"),Er=Symbol.for("react.fragment"),pu=Symbol.for("react.strict_mode"),Jl=Symbol.for("react.profiler"),Hf=Symbol.for("react.provider"),Qf=Symbol.for("react.context"),mu=Symbol.for("react.forward_ref"),ea=Symbol.for("react.suspense"),ta=Symbol.for("react.suspense_list"),hu=Symbol.for("react.memo"),hn=Symbol.for("react.lazy"),Kf=Symbol.for("react.offscreen"),Mc=Symbol.iterator;function ho(e){return e===null||typeof e!="object"?null:(e=Mc&&e[Mc]||e["@@iterator"],typeof e=="function"?e:null)}var ce=Object.assign,wl;function No(e){if(wl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);wl=t&&t[1]||""}return`
`+wl+e}var bl=!1;function Sl(e,t){if(!e||bl)return"";bl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,l=s.length-1;1<=i&&0<=l&&o[i]!==s[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==s[l]){if(i!==1||l!==1)do if(i--,l--,0>l||o[i]!==s[l]){var a=`
`+o[i].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=i&&0<=l);break}}}finally{bl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?No(e):""}function lv(e){switch(e.tag){case 5:return No(e.type);case 16:return No("Lazy");case 13:return No("Suspense");case 19:return No("SuspenseList");case 0:case 2:case 15:return e=Sl(e.type,!1),e;case 11:return e=Sl(e.type.render,!1),e;case 1:return e=Sl(e.type,!0),e;default:return""}}function na(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Er:return"Fragment";case Sr:return"Portal";case Jl:return"Profiler";case pu:return"StrictMode";case ea:return"Suspense";case ta:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Qf:return(e.displayName||"Context")+".Consumer";case Hf:return(e._context.displayName||"Context")+".Provider";case mu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case hu:return t=e.displayName||null,t!==null?t:na(e.type)||"Memo";case hn:t=e._payload,e=e._init;try{return na(e(t))}catch{}}return null}function av(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return na(t);case 8:return t===pu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function In(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Yf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function uv(e){var t=Yf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ss(e){e._valueTracker||(e._valueTracker=uv(e))}function Gf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Yf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ri(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ra(e,t){var n=t.checked;return ce({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Oc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=In(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function qf(e,t){t=t.checked,t!=null&&fu(e,"checked",t,!1)}function oa(e,t){qf(e,t);var n=In(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?sa(e,t.type,n):t.hasOwnProperty("defaultValue")&&sa(e,t.type,In(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function _c(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function sa(e,t,n){(t!=="number"||ri(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ko=Array.isArray;function _r(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+In(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ia(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return ce({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Lc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(ko(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:In(n)}}function Xf(e,t){var n=In(t.value),r=In(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ic(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Zf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function la(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Zf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Es,Jf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Es=Es||document.createElement("div"),Es.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Es.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function $o(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var To={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},cv=["Webkit","ms","Moz","O"];Object.keys(To).forEach(function(e){cv.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),To[t]=To[e]})});function ep(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||To.hasOwnProperty(e)&&To[e]?(""+t).trim():t+"px"}function tp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=ep(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var dv=ce({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function aa(e,t){if(t){if(dv[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function ua(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ca=null;function gu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var da=null,Lr=null,Ir=null;function Dc(e){if(e=ds(e)){if(typeof da!="function")throw Error(R(280));var t=e.stateNode;t&&(t=Wi(t),da(e.stateNode,e.type,t))}}function np(e){Lr?Ir?Ir.push(e):Ir=[e]:Lr=e}function rp(){if(Lr){var e=Lr,t=Ir;if(Ir=Lr=null,Dc(e),t)for(e=0;e<t.length;e++)Dc(t[e])}}function op(e,t){return e(t)}function sp(){}var El=!1;function ip(e,t,n){if(El)return e(t,n);El=!0;try{return op(e,t,n)}finally{El=!1,(Lr!==null||Ir!==null)&&(sp(),rp())}}function Bo(e,t){var n=e.stateNode;if(n===null)return null;var r=Wi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var fa=!1;if(Xt)try{var go={};Object.defineProperty(go,"passive",{get:function(){fa=!0}}),window.addEventListener("test",go,go),window.removeEventListener("test",go,go)}catch{fa=!1}function fv(e,t,n,r,o,s,i,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var Ro=!1,oi=null,si=!1,pa=null,pv={onError:function(e){Ro=!0,oi=e}};function mv(e,t,n,r,o,s,i,l,a){Ro=!1,oi=null,fv.apply(pv,arguments)}function hv(e,t,n,r,o,s,i,l,a){if(mv.apply(this,arguments),Ro){if(Ro){var u=oi;Ro=!1,oi=null}else throw Error(R(198));si||(si=!0,pa=u)}}function mr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function lp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function zc(e){if(mr(e)!==e)throw Error(R(188))}function gv(e){var t=e.alternate;if(!t){if(t=mr(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return zc(o),e;if(s===r)return zc(o),t;s=s.sibling}throw Error(R(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,l=o.child;l;){if(l===n){i=!0,n=o,r=s;break}if(l===r){i=!0,r=o,n=s;break}l=l.sibling}if(!i){for(l=s.child;l;){if(l===n){i=!0,n=s,r=o;break}if(l===r){i=!0,r=s,n=o;break}l=l.sibling}if(!i)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function ap(e){return e=gv(e),e!==null?up(e):null}function up(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=up(e);if(t!==null)return t;e=e.sibling}return null}var cp=et.unstable_scheduleCallback,Fc=et.unstable_cancelCallback,vv=et.unstable_shouldYield,yv=et.unstable_requestPaint,pe=et.unstable_now,xv=et.unstable_getCurrentPriorityLevel,vu=et.unstable_ImmediatePriority,dp=et.unstable_UserBlockingPriority,ii=et.unstable_NormalPriority,wv=et.unstable_LowPriority,fp=et.unstable_IdlePriority,$i=null,Dt=null;function bv(e){if(Dt&&typeof Dt.onCommitFiberRoot=="function")try{Dt.onCommitFiberRoot($i,e,void 0,(e.current.flags&128)===128)}catch{}}var Ct=Math.clz32?Math.clz32:Cv,Sv=Math.log,Ev=Math.LN2;function Cv(e){return e>>>=0,e===0?32:31-(Sv(e)/Ev|0)|0}var Cs=64,Ns=4194304;function Po(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function li(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,i=n&268435455;if(i!==0){var l=i&~o;l!==0?r=Po(l):(s&=i,s!==0&&(r=Po(s)))}else i=n&~o,i!==0?r=Po(i):s!==0&&(r=Po(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ct(t),o=1<<n,r|=e[n],t&=~o;return r}function Nv(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function kv(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var i=31-Ct(s),l=1<<i,a=o[i];a===-1?(!(l&n)||l&r)&&(o[i]=Nv(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function ma(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function pp(){var e=Cs;return Cs<<=1,!(Cs&4194240)&&(Cs=64),e}function Cl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function us(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ct(t),e[t]=n}function Pv(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Ct(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function yu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ct(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ee=0;function mp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var hp,xu,gp,vp,yp,ha=!1,ks=[],jn=null,Tn=null,Rn=null,Uo=new Map,Vo=new Map,vn=[],jv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function $c(e,t){switch(e){case"focusin":case"focusout":jn=null;break;case"dragenter":case"dragleave":Tn=null;break;case"mouseover":case"mouseout":Rn=null;break;case"pointerover":case"pointerout":Uo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Vo.delete(t.pointerId)}}function vo(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=ds(t),t!==null&&xu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Tv(e,t,n,r,o){switch(t){case"focusin":return jn=vo(jn,e,t,n,r,o),!0;case"dragenter":return Tn=vo(Tn,e,t,n,r,o),!0;case"mouseover":return Rn=vo(Rn,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return Uo.set(s,vo(Uo.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,Vo.set(s,vo(Vo.get(s)||null,e,t,n,r,o)),!0}return!1}function xp(e){var t=qn(e.target);if(t!==null){var n=mr(t);if(n!==null){if(t=n.tag,t===13){if(t=lp(n),t!==null){e.blockedOn=t,yp(e.priority,function(){gp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ws(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ga(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ca=r,n.target.dispatchEvent(r),ca=null}else return t=ds(n),t!==null&&xu(t),e.blockedOn=n,!1;t.shift()}return!0}function Bc(e,t,n){Ws(e)&&n.delete(t)}function Rv(){ha=!1,jn!==null&&Ws(jn)&&(jn=null),Tn!==null&&Ws(Tn)&&(Tn=null),Rn!==null&&Ws(Rn)&&(Rn=null),Uo.forEach(Bc),Vo.forEach(Bc)}function yo(e,t){e.blockedOn===t&&(e.blockedOn=null,ha||(ha=!0,et.unstable_scheduleCallback(et.unstable_NormalPriority,Rv)))}function Wo(e){function t(o){return yo(o,e)}if(0<ks.length){yo(ks[0],e);for(var n=1;n<ks.length;n++){var r=ks[n];r.blockedOn===e&&(r.blockedOn=null)}}for(jn!==null&&yo(jn,e),Tn!==null&&yo(Tn,e),Rn!==null&&yo(Rn,e),Uo.forEach(t),Vo.forEach(t),n=0;n<vn.length;n++)r=vn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<vn.length&&(n=vn[0],n.blockedOn===null);)xp(n),n.blockedOn===null&&vn.shift()}var Dr=rn.ReactCurrentBatchConfig,ai=!0;function Av(e,t,n,r){var o=ee,s=Dr.transition;Dr.transition=null;try{ee=1,wu(e,t,n,r)}finally{ee=o,Dr.transition=s}}function Mv(e,t,n,r){var o=ee,s=Dr.transition;Dr.transition=null;try{ee=4,wu(e,t,n,r)}finally{ee=o,Dr.transition=s}}function wu(e,t,n,r){if(ai){var o=ga(e,t,n,r);if(o===null)_l(e,t,r,ui,n),$c(e,r);else if(Tv(o,e,t,n,r))r.stopPropagation();else if($c(e,r),t&4&&-1<jv.indexOf(e)){for(;o!==null;){var s=ds(o);if(s!==null&&hp(s),s=ga(e,t,n,r),s===null&&_l(e,t,r,ui,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else _l(e,t,r,null,n)}}var ui=null;function ga(e,t,n,r){if(ui=null,e=gu(r),e=qn(e),e!==null)if(t=mr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=lp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ui=e,null}function wp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(xv()){case vu:return 1;case dp:return 4;case ii:case wv:return 16;case fp:return 536870912;default:return 16}default:return 16}}var Cn=null,bu=null,Hs=null;function bp(){if(Hs)return Hs;var e,t=bu,n=t.length,r,o="value"in Cn?Cn.value:Cn.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[s-r];r++);return Hs=o.slice(e,1<r?1-r:void 0)}function Qs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ps(){return!0}function Uc(){return!1}function nt(e){function t(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Ps:Uc,this.isPropagationStopped=Uc,this}return ce(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ps)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ps)},persist:function(){},isPersistent:Ps}),t}var lo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Su=nt(lo),cs=ce({},lo,{view:0,detail:0}),Ov=nt(cs),Nl,kl,xo,Bi=ce({},cs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Eu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==xo&&(xo&&e.type==="mousemove"?(Nl=e.screenX-xo.screenX,kl=e.screenY-xo.screenY):kl=Nl=0,xo=e),Nl)},movementY:function(e){return"movementY"in e?e.movementY:kl}}),Vc=nt(Bi),_v=ce({},Bi,{dataTransfer:0}),Lv=nt(_v),Iv=ce({},cs,{relatedTarget:0}),Pl=nt(Iv),Dv=ce({},lo,{animationName:0,elapsedTime:0,pseudoElement:0}),zv=nt(Dv),Fv=ce({},lo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$v=nt(Fv),Bv=ce({},lo,{data:0}),Wc=nt(Bv),Uv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Vv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Wv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Hv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Wv[e])?!!t[e]:!1}function Eu(){return Hv}var Qv=ce({},cs,{key:function(e){if(e.key){var t=Uv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Qs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Vv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Eu,charCode:function(e){return e.type==="keypress"?Qs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Qs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Kv=nt(Qv),Yv=ce({},Bi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Hc=nt(Yv),Gv=ce({},cs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Eu}),qv=nt(Gv),Xv=ce({},lo,{propertyName:0,elapsedTime:0,pseudoElement:0}),Zv=nt(Xv),Jv=ce({},Bi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ey=nt(Jv),ty=[9,13,27,32],Cu=Xt&&"CompositionEvent"in window,Ao=null;Xt&&"documentMode"in document&&(Ao=document.documentMode);var ny=Xt&&"TextEvent"in window&&!Ao,Sp=Xt&&(!Cu||Ao&&8<Ao&&11>=Ao),Qc=" ",Kc=!1;function Ep(e,t){switch(e){case"keyup":return ty.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Cp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Cr=!1;function ry(e,t){switch(e){case"compositionend":return Cp(t);case"keypress":return t.which!==32?null:(Kc=!0,Qc);case"textInput":return e=t.data,e===Qc&&Kc?null:e;default:return null}}function oy(e,t){if(Cr)return e==="compositionend"||!Cu&&Ep(e,t)?(e=bp(),Hs=bu=Cn=null,Cr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Sp&&t.locale!=="ko"?null:t.data;default:return null}}var sy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Yc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!sy[e.type]:t==="textarea"}function Np(e,t,n,r){np(r),t=ci(t,"onChange"),0<t.length&&(n=new Su("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Mo=null,Ho=null;function iy(e){Ip(e,0)}function Ui(e){var t=Pr(e);if(Gf(t))return e}function ly(e,t){if(e==="change")return t}var kp=!1;if(Xt){var jl;if(Xt){var Tl="oninput"in document;if(!Tl){var Gc=document.createElement("div");Gc.setAttribute("oninput","return;"),Tl=typeof Gc.oninput=="function"}jl=Tl}else jl=!1;kp=jl&&(!document.documentMode||9<document.documentMode)}function qc(){Mo&&(Mo.detachEvent("onpropertychange",Pp),Ho=Mo=null)}function Pp(e){if(e.propertyName==="value"&&Ui(Ho)){var t=[];Np(t,Ho,e,gu(e)),ip(iy,t)}}function ay(e,t,n){e==="focusin"?(qc(),Mo=t,Ho=n,Mo.attachEvent("onpropertychange",Pp)):e==="focusout"&&qc()}function uy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ui(Ho)}function cy(e,t){if(e==="click")return Ui(t)}function dy(e,t){if(e==="input"||e==="change")return Ui(t)}function fy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var kt=typeof Object.is=="function"?Object.is:fy;function Qo(e,t){if(kt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Zl.call(t,o)||!kt(e[o],t[o]))return!1}return!0}function Xc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zc(e,t){var n=Xc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Xc(n)}}function jp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?jp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Tp(){for(var e=window,t=ri();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ri(e.document)}return t}function Nu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function py(e){var t=Tp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&jp(n.ownerDocument.documentElement,n)){if(r!==null&&Nu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=Zc(n,s);var i=Zc(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var my=Xt&&"documentMode"in document&&11>=document.documentMode,Nr=null,va=null,Oo=null,ya=!1;function Jc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ya||Nr==null||Nr!==ri(r)||(r=Nr,"selectionStart"in r&&Nu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Oo&&Qo(Oo,r)||(Oo=r,r=ci(va,"onSelect"),0<r.length&&(t=new Su("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Nr)))}function js(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:js("Animation","AnimationEnd"),animationiteration:js("Animation","AnimationIteration"),animationstart:js("Animation","AnimationStart"),transitionend:js("Transition","TransitionEnd")},Rl={},Rp={};Xt&&(Rp=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);function Vi(e){if(Rl[e])return Rl[e];if(!kr[e])return e;var t=kr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Rp)return Rl[e]=t[n];return e}var Ap=Vi("animationend"),Mp=Vi("animationiteration"),Op=Vi("animationstart"),_p=Vi("transitionend"),Lp=new Map,ed="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Un(e,t){Lp.set(e,t),pr(t,[e])}for(var Al=0;Al<ed.length;Al++){var Ml=ed[Al],hy=Ml.toLowerCase(),gy=Ml[0].toUpperCase()+Ml.slice(1);Un(hy,"on"+gy)}Un(Ap,"onAnimationEnd");Un(Mp,"onAnimationIteration");Un(Op,"onAnimationStart");Un("dblclick","onDoubleClick");Un("focusin","onFocus");Un("focusout","onBlur");Un(_p,"onTransitionEnd");Xr("onMouseEnter",["mouseout","mouseover"]);Xr("onMouseLeave",["mouseout","mouseover"]);Xr("onPointerEnter",["pointerout","pointerover"]);Xr("onPointerLeave",["pointerout","pointerover"]);pr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));pr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));pr("onBeforeInput",["compositionend","keypress","textInput","paste"]);pr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));pr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));pr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var jo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),vy=new Set("cancel close invalid load scroll toggle".split(" ").concat(jo));function td(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,hv(r,t,void 0,e),e.currentTarget=null}function Ip(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==s&&o.isPropagationStopped())break e;td(o,l,u),s=a}else for(i=0;i<r.length;i++){if(l=r[i],a=l.instance,u=l.currentTarget,l=l.listener,a!==s&&o.isPropagationStopped())break e;td(o,l,u),s=a}}}if(si)throw e=pa,si=!1,pa=null,e}function oe(e,t){var n=t[Ea];n===void 0&&(n=t[Ea]=new Set);var r=e+"__bubble";n.has(r)||(Dp(t,e,2,!1),n.add(r))}function Ol(e,t,n){var r=0;t&&(r|=4),Dp(n,e,r,t)}var Ts="_reactListening"+Math.random().toString(36).slice(2);function Ko(e){if(!e[Ts]){e[Ts]=!0,Wf.forEach(function(n){n!=="selectionchange"&&(vy.has(n)||Ol(n,!1,e),Ol(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ts]||(t[Ts]=!0,Ol("selectionchange",!1,t))}}function Dp(e,t,n,r){switch(wp(t)){case 1:var o=Av;break;case 4:o=Mv;break;default:o=wu}n=o.bind(null,t,n,e),o=void 0,!fa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function _l(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var a=i.tag;if((a===3||a===4)&&(a=i.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;i=i.return}for(;l!==null;){if(i=qn(l),i===null)return;if(a=i.tag,a===5||a===6){r=s=i;continue e}l=l.parentNode}}r=r.return}ip(function(){var u=s,d=gu(n),p=[];e:{var h=Lp.get(e);if(h!==void 0){var f=Su,b=e;switch(e){case"keypress":if(Qs(n)===0)break e;case"keydown":case"keyup":f=Kv;break;case"focusin":b="focus",f=Pl;break;case"focusout":b="blur",f=Pl;break;case"beforeblur":case"afterblur":f=Pl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":f=Vc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":f=Lv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":f=qv;break;case Ap:case Mp:case Op:f=zv;break;case _p:f=Zv;break;case"scroll":f=Ov;break;case"wheel":f=ey;break;case"copy":case"cut":case"paste":f=$v;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":f=Hc}var y=(t&4)!==0,x=!y&&e==="scroll",g=y?h!==null?h+"Capture":null:h;y=[];for(var m=u,v;m!==null;){v=m;var S=v.stateNode;if(v.tag===5&&S!==null&&(v=S,g!==null&&(S=Bo(m,g),S!=null&&y.push(Yo(m,S,v)))),x)break;m=m.return}0<y.length&&(h=new f(h,b,null,n,d),p.push({event:h,listeners:y}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",f=e==="mouseout"||e==="pointerout",h&&n!==ca&&(b=n.relatedTarget||n.fromElement)&&(qn(b)||b[Zt]))break e;if((f||h)&&(h=d.window===d?d:(h=d.ownerDocument)?h.defaultView||h.parentWindow:window,f?(b=n.relatedTarget||n.toElement,f=u,b=b?qn(b):null,b!==null&&(x=mr(b),b!==x||b.tag!==5&&b.tag!==6)&&(b=null)):(f=null,b=u),f!==b)){if(y=Vc,S="onMouseLeave",g="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(y=Hc,S="onPointerLeave",g="onPointerEnter",m="pointer"),x=f==null?h:Pr(f),v=b==null?h:Pr(b),h=new y(S,m+"leave",f,n,d),h.target=x,h.relatedTarget=v,S=null,qn(d)===u&&(y=new y(g,m+"enter",b,n,d),y.target=v,y.relatedTarget=x,S=y),x=S,f&&b)t:{for(y=f,g=b,m=0,v=y;v;v=br(v))m++;for(v=0,S=g;S;S=br(S))v++;for(;0<m-v;)y=br(y),m--;for(;0<v-m;)g=br(g),v--;for(;m--;){if(y===g||g!==null&&y===g.alternate)break t;y=br(y),g=br(g)}y=null}else y=null;f!==null&&nd(p,h,f,y,!1),b!==null&&x!==null&&nd(p,x,b,y,!0)}}e:{if(h=u?Pr(u):window,f=h.nodeName&&h.nodeName.toLowerCase(),f==="select"||f==="input"&&h.type==="file")var E=ly;else if(Yc(h))if(kp)E=dy;else{E=uy;var C=ay}else(f=h.nodeName)&&f.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(E=cy);if(E&&(E=E(e,u))){Np(p,E,n,d);break e}C&&C(e,h,u),e==="focusout"&&(C=h._wrapperState)&&C.controlled&&h.type==="number"&&sa(h,"number",h.value)}switch(C=u?Pr(u):window,e){case"focusin":(Yc(C)||C.contentEditable==="true")&&(Nr=C,va=u,Oo=null);break;case"focusout":Oo=va=Nr=null;break;case"mousedown":ya=!0;break;case"contextmenu":case"mouseup":case"dragend":ya=!1,Jc(p,n,d);break;case"selectionchange":if(my)break;case"keydown":case"keyup":Jc(p,n,d)}var N;if(Cu)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else Cr?Ep(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Sp&&n.locale!=="ko"&&(Cr||T!=="onCompositionStart"?T==="onCompositionEnd"&&Cr&&(N=bp()):(Cn=d,bu="value"in Cn?Cn.value:Cn.textContent,Cr=!0)),C=ci(u,T),0<C.length&&(T=new Wc(T,e,null,n,d),p.push({event:T,listeners:C}),N?T.data=N:(N=Cp(n),N!==null&&(T.data=N)))),(N=ny?ry(e,n):oy(e,n))&&(u=ci(u,"onBeforeInput"),0<u.length&&(d=new Wc("onBeforeInput","beforeinput",null,n,d),p.push({event:d,listeners:u}),d.data=N))}Ip(p,t)})}function Yo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ci(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=Bo(e,n),s!=null&&r.unshift(Yo(e,s,o)),s=Bo(e,t),s!=null&&r.push(Yo(e,s,o))),e=e.return}return r}function br(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function nd(e,t,n,r,o){for(var s=t._reactName,i=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,o?(a=Bo(n,s),a!=null&&i.unshift(Yo(n,a,l))):o||(a=Bo(n,s),a!=null&&i.push(Yo(n,a,l)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var yy=/\r\n?/g,xy=/\u0000|\uFFFD/g;function rd(e){return(typeof e=="string"?e:""+e).replace(yy,`
`).replace(xy,"")}function Rs(e,t,n){if(t=rd(t),rd(e)!==t&&n)throw Error(R(425))}function di(){}var xa=null,wa=null;function ba(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Sa=typeof setTimeout=="function"?setTimeout:void 0,wy=typeof clearTimeout=="function"?clearTimeout:void 0,od=typeof Promise=="function"?Promise:void 0,by=typeof queueMicrotask=="function"?queueMicrotask:typeof od<"u"?function(e){return od.resolve(null).then(e).catch(Sy)}:Sa;function Sy(e){setTimeout(function(){throw e})}function Ll(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Wo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Wo(t)}function An(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function sd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ao=Math.random().toString(36).slice(2),Lt="__reactFiber$"+ao,Go="__reactProps$"+ao,Zt="__reactContainer$"+ao,Ea="__reactEvents$"+ao,Ey="__reactListeners$"+ao,Cy="__reactHandles$"+ao;function qn(e){var t=e[Lt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Zt]||n[Lt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=sd(e);e!==null;){if(n=e[Lt])return n;e=sd(e)}return t}e=n,n=e.parentNode}return null}function ds(e){return e=e[Lt]||e[Zt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Pr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function Wi(e){return e[Go]||null}var Ca=[],jr=-1;function Vn(e){return{current:e}}function se(e){0>jr||(e.current=Ca[jr],Ca[jr]=null,jr--)}function ne(e,t){jr++,Ca[jr]=e.current,e.current=t}var Dn={},Oe=Vn(Dn),We=Vn(!1),lr=Dn;function Zr(e,t){var n=e.type.contextTypes;if(!n)return Dn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function He(e){return e=e.childContextTypes,e!=null}function fi(){se(We),se(Oe)}function id(e,t,n){if(Oe.current!==Dn)throw Error(R(168));ne(Oe,t),ne(We,n)}function zp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(R(108,av(e)||"Unknown",o));return ce({},n,r)}function pi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Dn,lr=Oe.current,ne(Oe,e),ne(We,We.current),!0}function ld(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=zp(e,t,lr),r.__reactInternalMemoizedMergedChildContext=e,se(We),se(Oe),ne(Oe,e)):se(We),ne(We,n)}var Ht=null,Hi=!1,Il=!1;function Fp(e){Ht===null?Ht=[e]:Ht.push(e)}function Ny(e){Hi=!0,Fp(e)}function Wn(){if(!Il&&Ht!==null){Il=!0;var e=0,t=ee;try{var n=Ht;for(ee=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ht=null,Hi=!1}catch(o){throw Ht!==null&&(Ht=Ht.slice(e+1)),cp(vu,Wn),o}finally{ee=t,Il=!1}}return null}var Tr=[],Rr=0,mi=null,hi=0,lt=[],at=0,ar=null,Kt=1,Yt="";function Yn(e,t){Tr[Rr++]=hi,Tr[Rr++]=mi,mi=e,hi=t}function $p(e,t,n){lt[at++]=Kt,lt[at++]=Yt,lt[at++]=ar,ar=e;var r=Kt;e=Yt;var o=32-Ct(r)-1;r&=~(1<<o),n+=1;var s=32-Ct(t)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Kt=1<<32-Ct(t)+o|n<<o|r,Yt=s+e}else Kt=1<<s|n<<o|r,Yt=e}function ku(e){e.return!==null&&(Yn(e,1),$p(e,1,0))}function Pu(e){for(;e===mi;)mi=Tr[--Rr],Tr[Rr]=null,hi=Tr[--Rr],Tr[Rr]=null;for(;e===ar;)ar=lt[--at],lt[at]=null,Yt=lt[--at],lt[at]=null,Kt=lt[--at],lt[at]=null}var Ze=null,Xe=null,le=!1,Et=null;function Bp(e,t){var n=ut(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ad(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ze=e,Xe=An(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ze=e,Xe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=ar!==null?{id:Kt,overflow:Yt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ut(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ze=e,Xe=null,!0):!1;default:return!1}}function Na(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ka(e){if(le){var t=Xe;if(t){var n=t;if(!ad(e,t)){if(Na(e))throw Error(R(418));t=An(n.nextSibling);var r=Ze;t&&ad(e,t)?Bp(r,n):(e.flags=e.flags&-4097|2,le=!1,Ze=e)}}else{if(Na(e))throw Error(R(418));e.flags=e.flags&-4097|2,le=!1,Ze=e}}}function ud(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ze=e}function As(e){if(e!==Ze)return!1;if(!le)return ud(e),le=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ba(e.type,e.memoizedProps)),t&&(t=Xe)){if(Na(e))throw Up(),Error(R(418));for(;t;)Bp(e,t),t=An(t.nextSibling)}if(ud(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Xe=An(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Xe=null}}else Xe=Ze?An(e.stateNode.nextSibling):null;return!0}function Up(){for(var e=Xe;e;)e=An(e.nextSibling)}function Jr(){Xe=Ze=null,le=!1}function ju(e){Et===null?Et=[e]:Et.push(e)}var ky=rn.ReactCurrentBatchConfig;function wo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(i){var l=o.refs;i===null?delete l[s]:l[s]=i},t._stringRef=s,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function Ms(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function cd(e){var t=e._init;return t(e._payload)}function Vp(e){function t(g,m){if(e){var v=g.deletions;v===null?(g.deletions=[m],g.flags|=16):v.push(m)}}function n(g,m){if(!e)return null;for(;m!==null;)t(g,m),m=m.sibling;return null}function r(g,m){for(g=new Map;m!==null;)m.key!==null?g.set(m.key,m):g.set(m.index,m),m=m.sibling;return g}function o(g,m){return g=Ln(g,m),g.index=0,g.sibling=null,g}function s(g,m,v){return g.index=v,e?(v=g.alternate,v!==null?(v=v.index,v<m?(g.flags|=2,m):v):(g.flags|=2,m)):(g.flags|=1048576,m)}function i(g){return e&&g.alternate===null&&(g.flags|=2),g}function l(g,m,v,S){return m===null||m.tag!==6?(m=Vl(v,g.mode,S),m.return=g,m):(m=o(m,v),m.return=g,m)}function a(g,m,v,S){var E=v.type;return E===Er?d(g,m,v.props.children,S,v.key):m!==null&&(m.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===hn&&cd(E)===m.type)?(S=o(m,v.props),S.ref=wo(g,m,v),S.return=g,S):(S=Js(v.type,v.key,v.props,null,g.mode,S),S.ref=wo(g,m,v),S.return=g,S)}function u(g,m,v,S){return m===null||m.tag!==4||m.stateNode.containerInfo!==v.containerInfo||m.stateNode.implementation!==v.implementation?(m=Wl(v,g.mode,S),m.return=g,m):(m=o(m,v.children||[]),m.return=g,m)}function d(g,m,v,S,E){return m===null||m.tag!==7?(m=ir(v,g.mode,S,E),m.return=g,m):(m=o(m,v),m.return=g,m)}function p(g,m,v){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Vl(""+m,g.mode,v),m.return=g,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case bs:return v=Js(m.type,m.key,m.props,null,g.mode,v),v.ref=wo(g,null,m),v.return=g,v;case Sr:return m=Wl(m,g.mode,v),m.return=g,m;case hn:var S=m._init;return p(g,S(m._payload),v)}if(ko(m)||ho(m))return m=ir(m,g.mode,v,null),m.return=g,m;Ms(g,m)}return null}function h(g,m,v,S){var E=m!==null?m.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return E!==null?null:l(g,m,""+v,S);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case bs:return v.key===E?a(g,m,v,S):null;case Sr:return v.key===E?u(g,m,v,S):null;case hn:return E=v._init,h(g,m,E(v._payload),S)}if(ko(v)||ho(v))return E!==null?null:d(g,m,v,S,null);Ms(g,v)}return null}function f(g,m,v,S,E){if(typeof S=="string"&&S!==""||typeof S=="number")return g=g.get(v)||null,l(m,g,""+S,E);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case bs:return g=g.get(S.key===null?v:S.key)||null,a(m,g,S,E);case Sr:return g=g.get(S.key===null?v:S.key)||null,u(m,g,S,E);case hn:var C=S._init;return f(g,m,v,C(S._payload),E)}if(ko(S)||ho(S))return g=g.get(v)||null,d(m,g,S,E,null);Ms(m,S)}return null}function b(g,m,v,S){for(var E=null,C=null,N=m,T=m=0,O=null;N!==null&&T<v.length;T++){N.index>T?(O=N,N=null):O=N.sibling;var M=h(g,N,v[T],S);if(M===null){N===null&&(N=O);break}e&&N&&M.alternate===null&&t(g,N),m=s(M,m,T),C===null?E=M:C.sibling=M,C=M,N=O}if(T===v.length)return n(g,N),le&&Yn(g,T),E;if(N===null){for(;T<v.length;T++)N=p(g,v[T],S),N!==null&&(m=s(N,m,T),C===null?E=N:C.sibling=N,C=N);return le&&Yn(g,T),E}for(N=r(g,N);T<v.length;T++)O=f(N,g,T,v[T],S),O!==null&&(e&&O.alternate!==null&&N.delete(O.key===null?T:O.key),m=s(O,m,T),C===null?E=O:C.sibling=O,C=O);return e&&N.forEach(function(F){return t(g,F)}),le&&Yn(g,T),E}function y(g,m,v,S){var E=ho(v);if(typeof E!="function")throw Error(R(150));if(v=E.call(v),v==null)throw Error(R(151));for(var C=E=null,N=m,T=m=0,O=null,M=v.next();N!==null&&!M.done;T++,M=v.next()){N.index>T?(O=N,N=null):O=N.sibling;var F=h(g,N,M.value,S);if(F===null){N===null&&(N=O);break}e&&N&&F.alternate===null&&t(g,N),m=s(F,m,T),C===null?E=F:C.sibling=F,C=F,N=O}if(M.done)return n(g,N),le&&Yn(g,T),E;if(N===null){for(;!M.done;T++,M=v.next())M=p(g,M.value,S),M!==null&&(m=s(M,m,T),C===null?E=M:C.sibling=M,C=M);return le&&Yn(g,T),E}for(N=r(g,N);!M.done;T++,M=v.next())M=f(N,g,T,M.value,S),M!==null&&(e&&M.alternate!==null&&N.delete(M.key===null?T:M.key),m=s(M,m,T),C===null?E=M:C.sibling=M,C=M);return e&&N.forEach(function(D){return t(g,D)}),le&&Yn(g,T),E}function x(g,m,v,S){if(typeof v=="object"&&v!==null&&v.type===Er&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case bs:e:{for(var E=v.key,C=m;C!==null;){if(C.key===E){if(E=v.type,E===Er){if(C.tag===7){n(g,C.sibling),m=o(C,v.props.children),m.return=g,g=m;break e}}else if(C.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===hn&&cd(E)===C.type){n(g,C.sibling),m=o(C,v.props),m.ref=wo(g,C,v),m.return=g,g=m;break e}n(g,C);break}else t(g,C);C=C.sibling}v.type===Er?(m=ir(v.props.children,g.mode,S,v.key),m.return=g,g=m):(S=Js(v.type,v.key,v.props,null,g.mode,S),S.ref=wo(g,m,v),S.return=g,g=S)}return i(g);case Sr:e:{for(C=v.key;m!==null;){if(m.key===C)if(m.tag===4&&m.stateNode.containerInfo===v.containerInfo&&m.stateNode.implementation===v.implementation){n(g,m.sibling),m=o(m,v.children||[]),m.return=g,g=m;break e}else{n(g,m);break}else t(g,m);m=m.sibling}m=Wl(v,g.mode,S),m.return=g,g=m}return i(g);case hn:return C=v._init,x(g,m,C(v._payload),S)}if(ko(v))return b(g,m,v,S);if(ho(v))return y(g,m,v,S);Ms(g,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,m!==null&&m.tag===6?(n(g,m.sibling),m=o(m,v),m.return=g,g=m):(n(g,m),m=Vl(v,g.mode,S),m.return=g,g=m),i(g)):n(g,m)}return x}var eo=Vp(!0),Wp=Vp(!1),gi=Vn(null),vi=null,Ar=null,Tu=null;function Ru(){Tu=Ar=vi=null}function Au(e){var t=gi.current;se(gi),e._currentValue=t}function Pa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function zr(e,t){vi=e,Tu=Ar=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ve=!0),e.firstContext=null)}function dt(e){var t=e._currentValue;if(Tu!==e)if(e={context:e,memoizedValue:t,next:null},Ar===null){if(vi===null)throw Error(R(308));Ar=e,vi.dependencies={lanes:0,firstContext:e}}else Ar=Ar.next=e;return t}var Xn=null;function Mu(e){Xn===null?Xn=[e]:Xn.push(e)}function Hp(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Mu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Jt(e,r)}function Jt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var gn=!1;function Ou(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Qp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function qt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Mn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,X&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Jt(e,n)}return o=r.interleaved,o===null?(t.next=t,Mu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Jt(e,n)}function Ks(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,yu(e,n)}}function dd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function yi(e,t,n,r){var o=e.updateQueue;gn=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,u=a.next;a.next=null,i===null?s=u:i.next=u,i=a;var d=e.alternate;d!==null&&(d=d.updateQueue,l=d.lastBaseUpdate,l!==i&&(l===null?d.firstBaseUpdate=u:l.next=u,d.lastBaseUpdate=a))}if(s!==null){var p=o.baseState;i=0,d=u=a=null,l=s;do{var h=l.lane,f=l.eventTime;if((r&h)===h){d!==null&&(d=d.next={eventTime:f,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var b=e,y=l;switch(h=t,f=n,y.tag){case 1:if(b=y.payload,typeof b=="function"){p=b.call(f,p,h);break e}p=b;break e;case 3:b.flags=b.flags&-65537|128;case 0:if(b=y.payload,h=typeof b=="function"?b.call(f,p,h):b,h==null)break e;p=ce({},p,h);break e;case 2:gn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,h=o.effects,h===null?o.effects=[l]:h.push(l))}else f={eventTime:f,lane:h,tag:l.tag,payload:l.payload,callback:l.callback,next:null},d===null?(u=d=f,a=p):d=d.next=f,i|=h;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;h=l,l=h.next,h.next=null,o.lastBaseUpdate=h,o.shared.pending=null}}while(!0);if(d===null&&(a=p),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);cr|=i,e.lanes=i,e.memoizedState=p}}function fd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(R(191,o));o.call(r)}}}var fs={},zt=Vn(fs),qo=Vn(fs),Xo=Vn(fs);function Zn(e){if(e===fs)throw Error(R(174));return e}function _u(e,t){switch(ne(Xo,t),ne(qo,e),ne(zt,fs),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:la(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=la(t,e)}se(zt),ne(zt,t)}function to(){se(zt),se(qo),se(Xo)}function Kp(e){Zn(Xo.current);var t=Zn(zt.current),n=la(t,e.type);t!==n&&(ne(qo,e),ne(zt,n))}function Lu(e){qo.current===e&&(se(zt),se(qo))}var ae=Vn(0);function xi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Dl=[];function Iu(){for(var e=0;e<Dl.length;e++)Dl[e]._workInProgressVersionPrimary=null;Dl.length=0}var Ys=rn.ReactCurrentDispatcher,zl=rn.ReactCurrentBatchConfig,ur=0,ue=null,ve=null,we=null,wi=!1,_o=!1,Zo=0,Py=0;function Pe(){throw Error(R(321))}function Du(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!kt(e[n],t[n]))return!1;return!0}function zu(e,t,n,r,o,s){if(ur=s,ue=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ys.current=e===null||e.memoizedState===null?Ay:My,e=n(r,o),_o){s=0;do{if(_o=!1,Zo=0,25<=s)throw Error(R(301));s+=1,we=ve=null,t.updateQueue=null,Ys.current=Oy,e=n(r,o)}while(_o)}if(Ys.current=bi,t=ve!==null&&ve.next!==null,ur=0,we=ve=ue=null,wi=!1,t)throw Error(R(300));return e}function Fu(){var e=Zo!==0;return Zo=0,e}function At(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return we===null?ue.memoizedState=we=e:we=we.next=e,we}function ft(){if(ve===null){var e=ue.alternate;e=e!==null?e.memoizedState:null}else e=ve.next;var t=we===null?ue.memoizedState:we.next;if(t!==null)we=t,ve=e;else{if(e===null)throw Error(R(310));ve=e,e={memoizedState:ve.memoizedState,baseState:ve.baseState,baseQueue:ve.baseQueue,queue:ve.queue,next:null},we===null?ue.memoizedState=we=e:we=we.next=e}return we}function Jo(e,t){return typeof t=="function"?t(e):t}function Fl(e){var t=ft(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=ve,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var l=i=null,a=null,u=s;do{var d=u.lane;if((ur&d)===d)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var p={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=p,i=r):a=a.next=p,ue.lanes|=d,cr|=d}u=u.next}while(u!==null&&u!==s);a===null?i=r:a.next=l,kt(r,t.memoizedState)||(Ve=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,ue.lanes|=s,cr|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function $l(e){var t=ft(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=e(s,i.action),i=i.next;while(i!==o);kt(s,t.memoizedState)||(Ve=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Yp(){}function Gp(e,t){var n=ue,r=ft(),o=t(),s=!kt(r.memoizedState,o);if(s&&(r.memoizedState=o,Ve=!0),r=r.queue,$u(Zp.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||we!==null&&we.memoizedState.tag&1){if(n.flags|=2048,es(9,Xp.bind(null,n,r,o,t),void 0,null),be===null)throw Error(R(349));ur&30||qp(n,t,o)}return o}function qp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Xp(e,t,n,r){t.value=n,t.getSnapshot=r,Jp(t)&&em(e)}function Zp(e,t,n){return n(function(){Jp(t)&&em(e)})}function Jp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!kt(e,n)}catch{return!0}}function em(e){var t=Jt(e,1);t!==null&&Nt(t,e,1,-1)}function pd(e){var t=At();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Jo,lastRenderedState:e},t.queue=e,e=e.dispatch=Ry.bind(null,ue,e),[t.memoizedState,e]}function es(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function tm(){return ft().memoizedState}function Gs(e,t,n,r){var o=At();ue.flags|=e,o.memoizedState=es(1|t,n,void 0,r===void 0?null:r)}function Qi(e,t,n,r){var o=ft();r=r===void 0?null:r;var s=void 0;if(ve!==null){var i=ve.memoizedState;if(s=i.destroy,r!==null&&Du(r,i.deps)){o.memoizedState=es(t,n,s,r);return}}ue.flags|=e,o.memoizedState=es(1|t,n,s,r)}function md(e,t){return Gs(8390656,8,e,t)}function $u(e,t){return Qi(2048,8,e,t)}function nm(e,t){return Qi(4,2,e,t)}function rm(e,t){return Qi(4,4,e,t)}function om(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function sm(e,t,n){return n=n!=null?n.concat([e]):null,Qi(4,4,om.bind(null,t,e),n)}function Bu(){}function im(e,t){var n=ft();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Du(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function lm(e,t){var n=ft();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Du(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function am(e,t,n){return ur&21?(kt(n,t)||(n=pp(),ue.lanes|=n,cr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ve=!0),e.memoizedState=n)}function jy(e,t){var n=ee;ee=n!==0&&4>n?n:4,e(!0);var r=zl.transition;zl.transition={};try{e(!1),t()}finally{ee=n,zl.transition=r}}function um(){return ft().memoizedState}function Ty(e,t,n){var r=_n(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},cm(e))dm(t,n);else if(n=Hp(e,t,n,r),n!==null){var o=ze();Nt(n,e,r,o),fm(n,t,r)}}function Ry(e,t,n){var r=_n(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(cm(e))dm(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,l=s(i,n);if(o.hasEagerState=!0,o.eagerState=l,kt(l,i)){var a=t.interleaved;a===null?(o.next=o,Mu(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Hp(e,t,o,r),n!==null&&(o=ze(),Nt(n,e,r,o),fm(n,t,r))}}function cm(e){var t=e.alternate;return e===ue||t!==null&&t===ue}function dm(e,t){_o=wi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function fm(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,yu(e,n)}}var bi={readContext:dt,useCallback:Pe,useContext:Pe,useEffect:Pe,useImperativeHandle:Pe,useInsertionEffect:Pe,useLayoutEffect:Pe,useMemo:Pe,useReducer:Pe,useRef:Pe,useState:Pe,useDebugValue:Pe,useDeferredValue:Pe,useTransition:Pe,useMutableSource:Pe,useSyncExternalStore:Pe,useId:Pe,unstable_isNewReconciler:!1},Ay={readContext:dt,useCallback:function(e,t){return At().memoizedState=[e,t===void 0?null:t],e},useContext:dt,useEffect:md,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Gs(4194308,4,om.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Gs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Gs(4,2,e,t)},useMemo:function(e,t){var n=At();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=At();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ty.bind(null,ue,e),[r.memoizedState,e]},useRef:function(e){var t=At();return e={current:e},t.memoizedState=e},useState:pd,useDebugValue:Bu,useDeferredValue:function(e){return At().memoizedState=e},useTransition:function(){var e=pd(!1),t=e[0];return e=jy.bind(null,e[1]),At().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ue,o=At();if(le){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),be===null)throw Error(R(349));ur&30||qp(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,md(Zp.bind(null,r,s,e),[e]),r.flags|=2048,es(9,Xp.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=At(),t=be.identifierPrefix;if(le){var n=Yt,r=Kt;n=(r&~(1<<32-Ct(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Zo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Py++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},My={readContext:dt,useCallback:im,useContext:dt,useEffect:$u,useImperativeHandle:sm,useInsertionEffect:nm,useLayoutEffect:rm,useMemo:lm,useReducer:Fl,useRef:tm,useState:function(){return Fl(Jo)},useDebugValue:Bu,useDeferredValue:function(e){var t=ft();return am(t,ve.memoizedState,e)},useTransition:function(){var e=Fl(Jo)[0],t=ft().memoizedState;return[e,t]},useMutableSource:Yp,useSyncExternalStore:Gp,useId:um,unstable_isNewReconciler:!1},Oy={readContext:dt,useCallback:im,useContext:dt,useEffect:$u,useImperativeHandle:sm,useInsertionEffect:nm,useLayoutEffect:rm,useMemo:lm,useReducer:$l,useRef:tm,useState:function(){return $l(Jo)},useDebugValue:Bu,useDeferredValue:function(e){var t=ft();return ve===null?t.memoizedState=e:am(t,ve.memoizedState,e)},useTransition:function(){var e=$l(Jo)[0],t=ft().memoizedState;return[e,t]},useMutableSource:Yp,useSyncExternalStore:Gp,useId:um,unstable_isNewReconciler:!1};function yt(e,t){if(e&&e.defaultProps){t=ce({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ja(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ce({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ki={isMounted:function(e){return(e=e._reactInternals)?mr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ze(),o=_n(e),s=qt(r,o);s.payload=t,n!=null&&(s.callback=n),t=Mn(e,s,o),t!==null&&(Nt(t,e,o,r),Ks(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ze(),o=_n(e),s=qt(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Mn(e,s,o),t!==null&&(Nt(t,e,o,r),Ks(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ze(),r=_n(e),o=qt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Mn(e,o,r),t!==null&&(Nt(t,e,r,n),Ks(t,e,r))}};function hd(e,t,n,r,o,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,i):t.prototype&&t.prototype.isPureReactComponent?!Qo(n,r)||!Qo(o,s):!0}function pm(e,t,n){var r=!1,o=Dn,s=t.contextType;return typeof s=="object"&&s!==null?s=dt(s):(o=He(t)?lr:Oe.current,r=t.contextTypes,s=(r=r!=null)?Zr(e,o):Dn),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ki,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function gd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ki.enqueueReplaceState(t,t.state,null)}function Ta(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Ou(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=dt(s):(s=He(t)?lr:Oe.current,o.context=Zr(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(ja(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Ki.enqueueReplaceState(o,o.state,null),yi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function no(e,t){try{var n="",r=t;do n+=lv(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function Bl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ra(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var _y=typeof WeakMap=="function"?WeakMap:Map;function mm(e,t,n){n=qt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ei||(Ei=!0,$a=r),Ra(e,t)},n}function hm(e,t,n){n=qt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ra(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Ra(e,t),typeof r!="function"&&(On===null?On=new Set([this]):On.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function vd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new _y;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Yy.bind(null,e,t,n),t.then(e,e))}function yd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function xd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=qt(-1,1),t.tag=2,Mn(n,t,1))),n.lanes|=1),e)}var Ly=rn.ReactCurrentOwner,Ve=!1;function Ie(e,t,n,r){t.child=e===null?Wp(t,null,n,r):eo(t,e.child,n,r)}function wd(e,t,n,r,o){n=n.render;var s=t.ref;return zr(t,o),r=zu(e,t,n,r,s,o),n=Fu(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,en(e,t,o)):(le&&n&&ku(t),t.flags|=1,Ie(e,t,r,o),t.child)}function bd(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!Gu(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,gm(e,t,s,r,o)):(e=Js(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:Qo,n(i,r)&&e.ref===t.ref)return en(e,t,o)}return t.flags|=1,e=Ln(s,r),e.ref=t.ref,e.return=t,t.child=e}function gm(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(Qo(s,r)&&e.ref===t.ref)if(Ve=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(Ve=!0);else return t.lanes=e.lanes,en(e,t,o)}return Aa(e,t,n,r,o)}function vm(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ne(Or,Ge),Ge|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ne(Or,Ge),Ge|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,ne(Or,Ge),Ge|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,ne(Or,Ge),Ge|=r;return Ie(e,t,o,n),t.child}function ym(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Aa(e,t,n,r,o){var s=He(n)?lr:Oe.current;return s=Zr(t,s),zr(t,o),n=zu(e,t,n,r,s,o),r=Fu(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,en(e,t,o)):(le&&r&&ku(t),t.flags|=1,Ie(e,t,n,o),t.child)}function Sd(e,t,n,r,o){if(He(n)){var s=!0;pi(t)}else s=!1;if(zr(t,o),t.stateNode===null)qs(e,t),pm(t,n,r),Ta(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,l=t.memoizedProps;i.props=l;var a=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=dt(u):(u=He(n)?lr:Oe.current,u=Zr(t,u));var d=n.getDerivedStateFromProps,p=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function";p||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(l!==r||a!==u)&&gd(t,i,r,u),gn=!1;var h=t.memoizedState;i.state=h,yi(t,r,i,o),a=t.memoizedState,l!==r||h!==a||We.current||gn?(typeof d=="function"&&(ja(t,n,d,r),a=t.memoizedState),(l=gn||hd(t,n,l,r,h,a,u))?(p||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),i.props=r,i.state=a,i.context=u,r=l):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Qp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:yt(t.type,l),i.props=u,p=t.pendingProps,h=i.context,a=n.contextType,typeof a=="object"&&a!==null?a=dt(a):(a=He(n)?lr:Oe.current,a=Zr(t,a));var f=n.getDerivedStateFromProps;(d=typeof f=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(l!==p||h!==a)&&gd(t,i,r,a),gn=!1,h=t.memoizedState,i.state=h,yi(t,r,i,o);var b=t.memoizedState;l!==p||h!==b||We.current||gn?(typeof f=="function"&&(ja(t,n,f,r),b=t.memoizedState),(u=gn||hd(t,n,u,r,h,b,a)||!1)?(d||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,b,a),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,b,a)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=b),i.props=r,i.state=b,i.context=a,r=u):(typeof i.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Ma(e,t,n,r,s,o)}function Ma(e,t,n,r,o,s){ym(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&ld(t,n,!1),en(e,t,s);r=t.stateNode,Ly.current=t;var l=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=eo(t,e.child,null,s),t.child=eo(t,null,l,s)):Ie(e,t,l,s),t.memoizedState=r.state,o&&ld(t,n,!0),t.child}function xm(e){var t=e.stateNode;t.pendingContext?id(e,t.pendingContext,t.pendingContext!==t.context):t.context&&id(e,t.context,!1),_u(e,t.containerInfo)}function Ed(e,t,n,r,o){return Jr(),ju(o),t.flags|=256,Ie(e,t,n,r),t.child}var Oa={dehydrated:null,treeContext:null,retryLane:0};function _a(e){return{baseLanes:e,cachePool:null,transitions:null}}function wm(e,t,n){var r=t.pendingProps,o=ae.current,s=!1,i=(t.flags&128)!==0,l;if((l=i)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ne(ae,o&1),e===null)return ka(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=qi(i,r,0,null),e=ir(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=_a(n),t.memoizedState=Oa,e):Uu(t,i));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return Iy(e,t,i,r,l,o,n);if(s){s=r.fallback,i=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Ln(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?s=Ln(l,s):(s=ir(s,i,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=e.child.memoizedState,i=i===null?_a(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=Oa,r}return s=e.child,e=s.sibling,r=Ln(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Uu(e,t){return t=qi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Os(e,t,n,r){return r!==null&&ju(r),eo(t,e.child,null,n),e=Uu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Iy(e,t,n,r,o,s,i){if(n)return t.flags&256?(t.flags&=-257,r=Bl(Error(R(422))),Os(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=qi({mode:"visible",children:r.children},o,0,null),s=ir(s,o,i,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&eo(t,e.child,null,i),t.child.memoizedState=_a(i),t.memoizedState=Oa,s);if(!(t.mode&1))return Os(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(R(419)),r=Bl(s,r,void 0),Os(e,t,i,r)}if(l=(i&e.childLanes)!==0,Ve||l){if(r=be,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,Jt(e,o),Nt(r,e,o,-1))}return Yu(),r=Bl(Error(R(421))),Os(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Gy.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,Xe=An(o.nextSibling),Ze=t,le=!0,Et=null,e!==null&&(lt[at++]=Kt,lt[at++]=Yt,lt[at++]=ar,Kt=e.id,Yt=e.overflow,ar=t),t=Uu(t,r.children),t.flags|=4096,t)}function Cd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Pa(e.return,t,n)}function Ul(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function bm(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if(Ie(e,t,r.children,n),r=ae.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Cd(e,n,t);else if(e.tag===19)Cd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ne(ae,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&xi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ul(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&xi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ul(t,!0,n,null,s);break;case"together":Ul(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function qs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function en(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),cr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=Ln(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ln(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Dy(e,t,n){switch(t.tag){case 3:xm(t),Jr();break;case 5:Kp(t);break;case 1:He(t.type)&&pi(t);break;case 4:_u(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ne(gi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ne(ae,ae.current&1),t.flags|=128,null):n&t.child.childLanes?wm(e,t,n):(ne(ae,ae.current&1),e=en(e,t,n),e!==null?e.sibling:null);ne(ae,ae.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return bm(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ne(ae,ae.current),r)break;return null;case 22:case 23:return t.lanes=0,vm(e,t,n)}return en(e,t,n)}var Sm,La,Em,Cm;Sm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};La=function(){};Em=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Zn(zt.current);var s=null;switch(n){case"input":o=ra(e,o),r=ra(e,r),s=[];break;case"select":o=ce({},o,{value:void 0}),r=ce({},r,{value:void 0}),s=[];break;case"textarea":o=ia(e,o),r=ia(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=di)}aa(n,r);var i;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(i in l)l.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Fo.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var a=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(i in l)!l.hasOwnProperty(i)||a&&a.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in a)a.hasOwnProperty(i)&&l[i]!==a[i]&&(n||(n={}),n[i]=a[i])}else n||(s||(s=[]),s.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Fo.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&oe("scroll",e),s||l===a||(s=[])):(s=s||[]).push(u,a))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};Cm=function(e,t,n,r){n!==r&&(t.flags|=4)};function bo(e,t){if(!le)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function je(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function zy(e,t,n){var r=t.pendingProps;switch(Pu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return je(t),null;case 1:return He(t.type)&&fi(),je(t),null;case 3:return r=t.stateNode,to(),se(We),se(Oe),Iu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(As(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Et!==null&&(Va(Et),Et=null))),La(e,t),je(t),null;case 5:Lu(t);var o=Zn(Xo.current);if(n=t.type,e!==null&&t.stateNode!=null)Em(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return je(t),null}if(e=Zn(zt.current),As(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[Lt]=t,r[Go]=s,e=(t.mode&1)!==0,n){case"dialog":oe("cancel",r),oe("close",r);break;case"iframe":case"object":case"embed":oe("load",r);break;case"video":case"audio":for(o=0;o<jo.length;o++)oe(jo[o],r);break;case"source":oe("error",r);break;case"img":case"image":case"link":oe("error",r),oe("load",r);break;case"details":oe("toggle",r);break;case"input":Oc(r,s),oe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},oe("invalid",r);break;case"textarea":Lc(r,s),oe("invalid",r)}aa(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var l=s[i];i==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&Rs(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&Rs(r.textContent,l,e),o=["children",""+l]):Fo.hasOwnProperty(i)&&l!=null&&i==="onScroll"&&oe("scroll",r)}switch(n){case"input":Ss(r),_c(r,s,!0);break;case"textarea":Ss(r),Ic(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=di)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Zf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Lt]=t,e[Go]=r,Sm(e,t,!1,!1),t.stateNode=e;e:{switch(i=ua(n,r),n){case"dialog":oe("cancel",e),oe("close",e),o=r;break;case"iframe":case"object":case"embed":oe("load",e),o=r;break;case"video":case"audio":for(o=0;o<jo.length;o++)oe(jo[o],e);o=r;break;case"source":oe("error",e),o=r;break;case"img":case"image":case"link":oe("error",e),oe("load",e),o=r;break;case"details":oe("toggle",e),o=r;break;case"input":Oc(e,r),o=ra(e,r),oe("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ce({},r,{value:void 0}),oe("invalid",e);break;case"textarea":Lc(e,r),o=ia(e,r),oe("invalid",e);break;default:o=r}aa(n,o),l=o;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?tp(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Jf(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&$o(e,a):typeof a=="number"&&$o(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Fo.hasOwnProperty(s)?a!=null&&s==="onScroll"&&oe("scroll",e):a!=null&&fu(e,s,a,i))}switch(n){case"input":Ss(e),_c(e,r,!1);break;case"textarea":Ss(e),Ic(e);break;case"option":r.value!=null&&e.setAttribute("value",""+In(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?_r(e,!!r.multiple,s,!1):r.defaultValue!=null&&_r(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=di)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return je(t),null;case 6:if(e&&t.stateNode!=null)Cm(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=Zn(Xo.current),Zn(zt.current),As(t)){if(r=t.stateNode,n=t.memoizedProps,r[Lt]=t,(s=r.nodeValue!==n)&&(e=Ze,e!==null))switch(e.tag){case 3:Rs(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Rs(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Lt]=t,t.stateNode=r}return je(t),null;case 13:if(se(ae),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(le&&Xe!==null&&t.mode&1&&!(t.flags&128))Up(),Jr(),t.flags|=98560,s=!1;else if(s=As(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(R(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(R(317));s[Lt]=t}else Jr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;je(t),s=!1}else Et!==null&&(Va(Et),Et=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ae.current&1?xe===0&&(xe=3):Yu())),t.updateQueue!==null&&(t.flags|=4),je(t),null);case 4:return to(),La(e,t),e===null&&Ko(t.stateNode.containerInfo),je(t),null;case 10:return Au(t.type._context),je(t),null;case 17:return He(t.type)&&fi(),je(t),null;case 19:if(se(ae),s=t.memoizedState,s===null)return je(t),null;if(r=(t.flags&128)!==0,i=s.rendering,i===null)if(r)bo(s,!1);else{if(xe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=xi(e),i!==null){for(t.flags|=128,bo(s,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ne(ae,ae.current&1|2),t.child}e=e.sibling}s.tail!==null&&pe()>ro&&(t.flags|=128,r=!0,bo(s,!1),t.lanes=4194304)}else{if(!r)if(e=xi(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),bo(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!le)return je(t),null}else 2*pe()-s.renderingStartTime>ro&&n!==1073741824&&(t.flags|=128,r=!0,bo(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(n=s.last,n!==null?n.sibling=i:t.child=i,s.last=i)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=pe(),t.sibling=null,n=ae.current,ne(ae,r?n&1|2:n&1),t):(je(t),null);case 22:case 23:return Ku(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ge&1073741824&&(je(t),t.subtreeFlags&6&&(t.flags|=8192)):je(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function Fy(e,t){switch(Pu(t),t.tag){case 1:return He(t.type)&&fi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return to(),se(We),se(Oe),Iu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Lu(t),null;case 13:if(se(ae),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));Jr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return se(ae),null;case 4:return to(),null;case 10:return Au(t.type._context),null;case 22:case 23:return Ku(),null;case 24:return null;default:return null}}var _s=!1,Me=!1,$y=typeof WeakSet=="function"?WeakSet:Set,I=null;function Mr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){fe(e,t,r)}else n.current=null}function Ia(e,t,n){try{n()}catch(r){fe(e,t,r)}}var Nd=!1;function By(e,t){if(xa=ai,e=Tp(),Nu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var i=0,l=-1,a=-1,u=0,d=0,p=e,h=null;t:for(;;){for(var f;p!==n||o!==0&&p.nodeType!==3||(l=i+o),p!==s||r!==0&&p.nodeType!==3||(a=i+r),p.nodeType===3&&(i+=p.nodeValue.length),(f=p.firstChild)!==null;)h=p,p=f;for(;;){if(p===e)break t;if(h===n&&++u===o&&(l=i),h===s&&++d===r&&(a=i),(f=p.nextSibling)!==null)break;p=h,h=p.parentNode}p=f}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(wa={focusedElem:e,selectionRange:n},ai=!1,I=t;I!==null;)if(t=I,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,I=e;else for(;I!==null;){t=I;try{var b=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(b!==null){var y=b.memoizedProps,x=b.memoizedState,g=t.stateNode,m=g.getSnapshotBeforeUpdate(t.elementType===t.type?y:yt(t.type,y),x);g.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(S){fe(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,I=e;break}I=t.return}return b=Nd,Nd=!1,b}function Lo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&Ia(t,n,s)}o=o.next}while(o!==r)}}function Yi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Da(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Nm(e){var t=e.alternate;t!==null&&(e.alternate=null,Nm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Lt],delete t[Go],delete t[Ea],delete t[Ey],delete t[Cy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function km(e){return e.tag===5||e.tag===3||e.tag===4}function kd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||km(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function za(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=di));else if(r!==4&&(e=e.child,e!==null))for(za(e,t,n),e=e.sibling;e!==null;)za(e,t,n),e=e.sibling}function Fa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Fa(e,t,n),e=e.sibling;e!==null;)Fa(e,t,n),e=e.sibling}var Se=null,St=!1;function dn(e,t,n){for(n=n.child;n!==null;)Pm(e,t,n),n=n.sibling}function Pm(e,t,n){if(Dt&&typeof Dt.onCommitFiberUnmount=="function")try{Dt.onCommitFiberUnmount($i,n)}catch{}switch(n.tag){case 5:Me||Mr(n,t);case 6:var r=Se,o=St;Se=null,dn(e,t,n),Se=r,St=o,Se!==null&&(St?(e=Se,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Se.removeChild(n.stateNode));break;case 18:Se!==null&&(St?(e=Se,n=n.stateNode,e.nodeType===8?Ll(e.parentNode,n):e.nodeType===1&&Ll(e,n),Wo(e)):Ll(Se,n.stateNode));break;case 4:r=Se,o=St,Se=n.stateNode.containerInfo,St=!0,dn(e,t,n),Se=r,St=o;break;case 0:case 11:case 14:case 15:if(!Me&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&Ia(n,t,i),o=o.next}while(o!==r)}dn(e,t,n);break;case 1:if(!Me&&(Mr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){fe(n,t,l)}dn(e,t,n);break;case 21:dn(e,t,n);break;case 22:n.mode&1?(Me=(r=Me)||n.memoizedState!==null,dn(e,t,n),Me=r):dn(e,t,n);break;default:dn(e,t,n)}}function Pd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new $y),t.forEach(function(r){var o=qy.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function gt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,i=t,l=i;e:for(;l!==null;){switch(l.tag){case 5:Se=l.stateNode,St=!1;break e;case 3:Se=l.stateNode.containerInfo,St=!0;break e;case 4:Se=l.stateNode.containerInfo,St=!0;break e}l=l.return}if(Se===null)throw Error(R(160));Pm(s,i,o),Se=null,St=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){fe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)jm(t,e),t=t.sibling}function jm(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(gt(t,e),Rt(e),r&4){try{Lo(3,e,e.return),Yi(3,e)}catch(y){fe(e,e.return,y)}try{Lo(5,e,e.return)}catch(y){fe(e,e.return,y)}}break;case 1:gt(t,e),Rt(e),r&512&&n!==null&&Mr(n,n.return);break;case 5:if(gt(t,e),Rt(e),r&512&&n!==null&&Mr(n,n.return),e.flags&32){var o=e.stateNode;try{$o(o,"")}catch(y){fe(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,i=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&qf(o,s),ua(l,i);var u=ua(l,s);for(i=0;i<a.length;i+=2){var d=a[i],p=a[i+1];d==="style"?tp(o,p):d==="dangerouslySetInnerHTML"?Jf(o,p):d==="children"?$o(o,p):fu(o,d,p,u)}switch(l){case"input":oa(o,s);break;case"textarea":Xf(o,s);break;case"select":var h=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var f=s.value;f!=null?_r(o,!!s.multiple,f,!1):h!==!!s.multiple&&(s.defaultValue!=null?_r(o,!!s.multiple,s.defaultValue,!0):_r(o,!!s.multiple,s.multiple?[]:"",!1))}o[Go]=s}catch(y){fe(e,e.return,y)}}break;case 6:if(gt(t,e),Rt(e),r&4){if(e.stateNode===null)throw Error(R(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(y){fe(e,e.return,y)}}break;case 3:if(gt(t,e),Rt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Wo(t.containerInfo)}catch(y){fe(e,e.return,y)}break;case 4:gt(t,e),Rt(e);break;case 13:gt(t,e),Rt(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(Hu=pe())),r&4&&Pd(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(Me=(u=Me)||d,gt(t,e),Me=u):gt(t,e),Rt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(I=e,d=e.child;d!==null;){for(p=I=d;I!==null;){switch(h=I,f=h.child,h.tag){case 0:case 11:case 14:case 15:Lo(4,h,h.return);break;case 1:Mr(h,h.return);var b=h.stateNode;if(typeof b.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,b.props=t.memoizedProps,b.state=t.memoizedState,b.componentWillUnmount()}catch(y){fe(r,n,y)}}break;case 5:Mr(h,h.return);break;case 22:if(h.memoizedState!==null){Td(p);continue}}f!==null?(f.return=h,I=f):Td(p)}d=d.sibling}e:for(d=null,p=e;;){if(p.tag===5){if(d===null){d=p;try{o=p.stateNode,u?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=p.stateNode,a=p.memoizedProps.style,i=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=ep("display",i))}catch(y){fe(e,e.return,y)}}}else if(p.tag===6){if(d===null)try{p.stateNode.nodeValue=u?"":p.memoizedProps}catch(y){fe(e,e.return,y)}}else if((p.tag!==22&&p.tag!==23||p.memoizedState===null||p===e)&&p.child!==null){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;p.sibling===null;){if(p.return===null||p.return===e)break e;d===p&&(d=null),p=p.return}d===p&&(d=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:gt(t,e),Rt(e),r&4&&Pd(e);break;case 21:break;default:gt(t,e),Rt(e)}}function Rt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(km(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&($o(o,""),r.flags&=-33);var s=kd(e);Fa(e,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,l=kd(e);za(e,l,i);break;default:throw Error(R(161))}}catch(a){fe(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Uy(e,t,n){I=e,Tm(e)}function Tm(e,t,n){for(var r=(e.mode&1)!==0;I!==null;){var o=I,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||_s;if(!i){var l=o.alternate,a=l!==null&&l.memoizedState!==null||Me;l=_s;var u=Me;if(_s=i,(Me=a)&&!u)for(I=o;I!==null;)i=I,a=i.child,i.tag===22&&i.memoizedState!==null?Rd(o):a!==null?(a.return=i,I=a):Rd(o);for(;s!==null;)I=s,Tm(s),s=s.sibling;I=o,_s=l,Me=u}jd(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,I=s):jd(e)}}function jd(e){for(;I!==null;){var t=I;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Me||Yi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Me)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:yt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&fd(t,s,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}fd(t,i,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var p=d.dehydrated;p!==null&&Wo(p)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}Me||t.flags&512&&Da(t)}catch(h){fe(t,t.return,h)}}if(t===e){I=null;break}if(n=t.sibling,n!==null){n.return=t.return,I=n;break}I=t.return}}function Td(e){for(;I!==null;){var t=I;if(t===e){I=null;break}var n=t.sibling;if(n!==null){n.return=t.return,I=n;break}I=t.return}}function Rd(e){for(;I!==null;){var t=I;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Yi(4,t)}catch(a){fe(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){fe(t,o,a)}}var s=t.return;try{Da(t)}catch(a){fe(t,s,a)}break;case 5:var i=t.return;try{Da(t)}catch(a){fe(t,i,a)}}}catch(a){fe(t,t.return,a)}if(t===e){I=null;break}var l=t.sibling;if(l!==null){l.return=t.return,I=l;break}I=t.return}}var Vy=Math.ceil,Si=rn.ReactCurrentDispatcher,Vu=rn.ReactCurrentOwner,ct=rn.ReactCurrentBatchConfig,X=0,be=null,he=null,Ee=0,Ge=0,Or=Vn(0),xe=0,ts=null,cr=0,Gi=0,Wu=0,Io=null,Ue=null,Hu=0,ro=1/0,Wt=null,Ei=!1,$a=null,On=null,Ls=!1,Nn=null,Ci=0,Do=0,Ba=null,Xs=-1,Zs=0;function ze(){return X&6?pe():Xs!==-1?Xs:Xs=pe()}function _n(e){return e.mode&1?X&2&&Ee!==0?Ee&-Ee:ky.transition!==null?(Zs===0&&(Zs=pp()),Zs):(e=ee,e!==0||(e=window.event,e=e===void 0?16:wp(e.type)),e):1}function Nt(e,t,n,r){if(50<Do)throw Do=0,Ba=null,Error(R(185));us(e,n,r),(!(X&2)||e!==be)&&(e===be&&(!(X&2)&&(Gi|=n),xe===4&&yn(e,Ee)),Qe(e,r),n===1&&X===0&&!(t.mode&1)&&(ro=pe()+500,Hi&&Wn()))}function Qe(e,t){var n=e.callbackNode;kv(e,t);var r=li(e,e===be?Ee:0);if(r===0)n!==null&&Fc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Fc(n),t===1)e.tag===0?Ny(Ad.bind(null,e)):Fp(Ad.bind(null,e)),by(function(){!(X&6)&&Wn()}),n=null;else{switch(mp(r)){case 1:n=vu;break;case 4:n=dp;break;case 16:n=ii;break;case 536870912:n=fp;break;default:n=ii}n=Dm(n,Rm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Rm(e,t){if(Xs=-1,Zs=0,X&6)throw Error(R(327));var n=e.callbackNode;if(Fr()&&e.callbackNode!==n)return null;var r=li(e,e===be?Ee:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ni(e,r);else{t=r;var o=X;X|=2;var s=Mm();(be!==e||Ee!==t)&&(Wt=null,ro=pe()+500,sr(e,t));do try{Qy();break}catch(l){Am(e,l)}while(!0);Ru(),Si.current=s,X=o,he!==null?t=0:(be=null,Ee=0,t=xe)}if(t!==0){if(t===2&&(o=ma(e),o!==0&&(r=o,t=Ua(e,o))),t===1)throw n=ts,sr(e,0),yn(e,r),Qe(e,pe()),n;if(t===6)yn(e,r);else{if(o=e.current.alternate,!(r&30)&&!Wy(o)&&(t=Ni(e,r),t===2&&(s=ma(e),s!==0&&(r=s,t=Ua(e,s))),t===1))throw n=ts,sr(e,0),yn(e,r),Qe(e,pe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:Gn(e,Ue,Wt);break;case 3:if(yn(e,r),(r&130023424)===r&&(t=Hu+500-pe(),10<t)){if(li(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){ze(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Sa(Gn.bind(null,e,Ue,Wt),t);break}Gn(e,Ue,Wt);break;case 4:if(yn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-Ct(r);s=1<<i,i=t[i],i>o&&(o=i),r&=~s}if(r=o,r=pe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Vy(r/1960))-r,10<r){e.timeoutHandle=Sa(Gn.bind(null,e,Ue,Wt),r);break}Gn(e,Ue,Wt);break;case 5:Gn(e,Ue,Wt);break;default:throw Error(R(329))}}}return Qe(e,pe()),e.callbackNode===n?Rm.bind(null,e):null}function Ua(e,t){var n=Io;return e.current.memoizedState.isDehydrated&&(sr(e,t).flags|=256),e=Ni(e,t),e!==2&&(t=Ue,Ue=n,t!==null&&Va(t)),e}function Va(e){Ue===null?Ue=e:Ue.push.apply(Ue,e)}function Wy(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!kt(s(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function yn(e,t){for(t&=~Wu,t&=~Gi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ct(t),r=1<<n;e[n]=-1,t&=~r}}function Ad(e){if(X&6)throw Error(R(327));Fr();var t=li(e,0);if(!(t&1))return Qe(e,pe()),null;var n=Ni(e,t);if(e.tag!==0&&n===2){var r=ma(e);r!==0&&(t=r,n=Ua(e,r))}if(n===1)throw n=ts,sr(e,0),yn(e,t),Qe(e,pe()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Gn(e,Ue,Wt),Qe(e,pe()),null}function Qu(e,t){var n=X;X|=1;try{return e(t)}finally{X=n,X===0&&(ro=pe()+500,Hi&&Wn())}}function dr(e){Nn!==null&&Nn.tag===0&&!(X&6)&&Fr();var t=X;X|=1;var n=ct.transition,r=ee;try{if(ct.transition=null,ee=1,e)return e()}finally{ee=r,ct.transition=n,X=t,!(X&6)&&Wn()}}function Ku(){Ge=Or.current,se(Or)}function sr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,wy(n)),he!==null)for(n=he.return;n!==null;){var r=n;switch(Pu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&fi();break;case 3:to(),se(We),se(Oe),Iu();break;case 5:Lu(r);break;case 4:to();break;case 13:se(ae);break;case 19:se(ae);break;case 10:Au(r.type._context);break;case 22:case 23:Ku()}n=n.return}if(be=e,he=e=Ln(e.current,null),Ee=Ge=t,xe=0,ts=null,Wu=Gi=cr=0,Ue=Io=null,Xn!==null){for(t=0;t<Xn.length;t++)if(n=Xn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}Xn=null}return e}function Am(e,t){do{var n=he;try{if(Ru(),Ys.current=bi,wi){for(var r=ue.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}wi=!1}if(ur=0,we=ve=ue=null,_o=!1,Zo=0,Vu.current=null,n===null||n.return===null){xe=1,ts=t,he=null;break}e:{var s=e,i=n.return,l=n,a=t;if(t=Ee,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,d=l,p=d.tag;if(!(d.mode&1)&&(p===0||p===11||p===15)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var f=yd(i);if(f!==null){f.flags&=-257,xd(f,i,l,s,t),f.mode&1&&vd(s,u,t),t=f,a=u;var b=t.updateQueue;if(b===null){var y=new Set;y.add(a),t.updateQueue=y}else b.add(a);break e}else{if(!(t&1)){vd(s,u,t),Yu();break e}a=Error(R(426))}}else if(le&&l.mode&1){var x=yd(i);if(x!==null){!(x.flags&65536)&&(x.flags|=256),xd(x,i,l,s,t),ju(no(a,l));break e}}s=a=no(a,l),xe!==4&&(xe=2),Io===null?Io=[s]:Io.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var g=mm(s,a,t);dd(s,g);break e;case 1:l=a;var m=s.type,v=s.stateNode;if(!(s.flags&128)&&(typeof m.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(On===null||!On.has(v)))){s.flags|=65536,t&=-t,s.lanes|=t;var S=hm(s,l,t);dd(s,S);break e}}s=s.return}while(s!==null)}_m(n)}catch(E){t=E,he===n&&n!==null&&(he=n=n.return);continue}break}while(!0)}function Mm(){var e=Si.current;return Si.current=bi,e===null?bi:e}function Yu(){(xe===0||xe===3||xe===2)&&(xe=4),be===null||!(cr&268435455)&&!(Gi&268435455)||yn(be,Ee)}function Ni(e,t){var n=X;X|=2;var r=Mm();(be!==e||Ee!==t)&&(Wt=null,sr(e,t));do try{Hy();break}catch(o){Am(e,o)}while(!0);if(Ru(),X=n,Si.current=r,he!==null)throw Error(R(261));return be=null,Ee=0,xe}function Hy(){for(;he!==null;)Om(he)}function Qy(){for(;he!==null&&!vv();)Om(he)}function Om(e){var t=Im(e.alternate,e,Ge);e.memoizedProps=e.pendingProps,t===null?_m(e):he=t,Vu.current=null}function _m(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Fy(n,t),n!==null){n.flags&=32767,he=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{xe=6,he=null;return}}else if(n=zy(n,t,Ge),n!==null){he=n;return}if(t=t.sibling,t!==null){he=t;return}he=t=e}while(t!==null);xe===0&&(xe=5)}function Gn(e,t,n){var r=ee,o=ct.transition;try{ct.transition=null,ee=1,Ky(e,t,n,r)}finally{ct.transition=o,ee=r}return null}function Ky(e,t,n,r){do Fr();while(Nn!==null);if(X&6)throw Error(R(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Pv(e,s),e===be&&(he=be=null,Ee=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ls||(Ls=!0,Dm(ii,function(){return Fr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=ct.transition,ct.transition=null;var i=ee;ee=1;var l=X;X|=4,Vu.current=null,By(e,n),jm(n,e),py(wa),ai=!!xa,wa=xa=null,e.current=n,Uy(n),yv(),X=l,ee=i,ct.transition=s}else e.current=n;if(Ls&&(Ls=!1,Nn=e,Ci=o),s=e.pendingLanes,s===0&&(On=null),bv(n.stateNode),Qe(e,pe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ei)throw Ei=!1,e=$a,$a=null,e;return Ci&1&&e.tag!==0&&Fr(),s=e.pendingLanes,s&1?e===Ba?Do++:(Do=0,Ba=e):Do=0,Wn(),null}function Fr(){if(Nn!==null){var e=mp(Ci),t=ct.transition,n=ee;try{if(ct.transition=null,ee=16>e?16:e,Nn===null)var r=!1;else{if(e=Nn,Nn=null,Ci=0,X&6)throw Error(R(331));var o=X;for(X|=4,I=e.current;I!==null;){var s=I,i=s.child;if(I.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(I=u;I!==null;){var d=I;switch(d.tag){case 0:case 11:case 15:Lo(8,d,s)}var p=d.child;if(p!==null)p.return=d,I=p;else for(;I!==null;){d=I;var h=d.sibling,f=d.return;if(Nm(d),d===u){I=null;break}if(h!==null){h.return=f,I=h;break}I=f}}}var b=s.alternate;if(b!==null){var y=b.child;if(y!==null){b.child=null;do{var x=y.sibling;y.sibling=null,y=x}while(y!==null)}}I=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,I=i;else e:for(;I!==null;){if(s=I,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Lo(9,s,s.return)}var g=s.sibling;if(g!==null){g.return=s.return,I=g;break e}I=s.return}}var m=e.current;for(I=m;I!==null;){i=I;var v=i.child;if(i.subtreeFlags&2064&&v!==null)v.return=i,I=v;else e:for(i=m;I!==null;){if(l=I,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Yi(9,l)}}catch(E){fe(l,l.return,E)}if(l===i){I=null;break e}var S=l.sibling;if(S!==null){S.return=l.return,I=S;break e}I=l.return}}if(X=o,Wn(),Dt&&typeof Dt.onPostCommitFiberRoot=="function")try{Dt.onPostCommitFiberRoot($i,e)}catch{}r=!0}return r}finally{ee=n,ct.transition=t}}return!1}function Md(e,t,n){t=no(n,t),t=mm(e,t,1),e=Mn(e,t,1),t=ze(),e!==null&&(us(e,1,t),Qe(e,t))}function fe(e,t,n){if(e.tag===3)Md(e,e,n);else for(;t!==null;){if(t.tag===3){Md(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(On===null||!On.has(r))){e=no(n,e),e=hm(t,e,1),t=Mn(t,e,1),e=ze(),t!==null&&(us(t,1,e),Qe(t,e));break}}t=t.return}}function Yy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ze(),e.pingedLanes|=e.suspendedLanes&n,be===e&&(Ee&n)===n&&(xe===4||xe===3&&(Ee&130023424)===Ee&&500>pe()-Hu?sr(e,0):Wu|=n),Qe(e,t)}function Lm(e,t){t===0&&(e.mode&1?(t=Ns,Ns<<=1,!(Ns&130023424)&&(Ns=4194304)):t=1);var n=ze();e=Jt(e,t),e!==null&&(us(e,t,n),Qe(e,n))}function Gy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Lm(e,n)}function qy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),Lm(e,n)}var Im;Im=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||We.current)Ve=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ve=!1,Dy(e,t,n);Ve=!!(e.flags&131072)}else Ve=!1,le&&t.flags&1048576&&$p(t,hi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;qs(e,t),e=t.pendingProps;var o=Zr(t,Oe.current);zr(t,n),o=zu(null,t,r,e,o,n);var s=Fu();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,He(r)?(s=!0,pi(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Ou(t),o.updater=Ki,t.stateNode=o,o._reactInternals=t,Ta(t,r,e,n),t=Ma(null,t,r,!0,s,n)):(t.tag=0,le&&s&&ku(t),Ie(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(qs(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Zy(r),e=yt(r,e),o){case 0:t=Aa(null,t,r,e,n);break e;case 1:t=Sd(null,t,r,e,n);break e;case 11:t=wd(null,t,r,e,n);break e;case 14:t=bd(null,t,r,yt(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:yt(r,o),Aa(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:yt(r,o),Sd(e,t,r,o,n);case 3:e:{if(xm(t),e===null)throw Error(R(387));r=t.pendingProps,s=t.memoizedState,o=s.element,Qp(e,t),yi(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=no(Error(R(423)),t),t=Ed(e,t,r,n,o);break e}else if(r!==o){o=no(Error(R(424)),t),t=Ed(e,t,r,n,o);break e}else for(Xe=An(t.stateNode.containerInfo.firstChild),Ze=t,le=!0,Et=null,n=Wp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Jr(),r===o){t=en(e,t,n);break e}Ie(e,t,r,n)}t=t.child}return t;case 5:return Kp(t),e===null&&ka(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,i=o.children,ba(r,o)?i=null:s!==null&&ba(r,s)&&(t.flags|=32),ym(e,t),Ie(e,t,i,n),t.child;case 6:return e===null&&ka(t),null;case 13:return wm(e,t,n);case 4:return _u(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=eo(t,null,r,n):Ie(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:yt(r,o),wd(e,t,r,o,n);case 7:return Ie(e,t,t.pendingProps,n),t.child;case 8:return Ie(e,t,t.pendingProps.children,n),t.child;case 12:return Ie(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,i=o.value,ne(gi,r._currentValue),r._currentValue=i,s!==null)if(kt(s.value,i)){if(s.children===o.children&&!We.current){t=en(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){i=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=qt(-1,n&-n),a.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?a.next=a:(a.next=d.next,d.next=a),u.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Pa(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)i=s.type===t.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(R(341));i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),Pa(i,n,t),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===t){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}Ie(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,zr(t,n),o=dt(o),r=r(o),t.flags|=1,Ie(e,t,r,n),t.child;case 14:return r=t.type,o=yt(r,t.pendingProps),o=yt(r.type,o),bd(e,t,r,o,n);case 15:return gm(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:yt(r,o),qs(e,t),t.tag=1,He(r)?(e=!0,pi(t)):e=!1,zr(t,n),pm(t,r,o),Ta(t,r,o,n),Ma(null,t,r,!0,e,n);case 19:return bm(e,t,n);case 22:return vm(e,t,n)}throw Error(R(156,t.tag))};function Dm(e,t){return cp(e,t)}function Xy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ut(e,t,n,r){return new Xy(e,t,n,r)}function Gu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Zy(e){if(typeof e=="function")return Gu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===mu)return 11;if(e===hu)return 14}return 2}function Ln(e,t){var n=e.alternate;return n===null?(n=ut(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Js(e,t,n,r,o,s){var i=2;if(r=e,typeof e=="function")Gu(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Er:return ir(n.children,o,s,t);case pu:i=8,o|=8;break;case Jl:return e=ut(12,n,t,o|2),e.elementType=Jl,e.lanes=s,e;case ea:return e=ut(13,n,t,o),e.elementType=ea,e.lanes=s,e;case ta:return e=ut(19,n,t,o),e.elementType=ta,e.lanes=s,e;case Kf:return qi(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Hf:i=10;break e;case Qf:i=9;break e;case mu:i=11;break e;case hu:i=14;break e;case hn:i=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=ut(i,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function ir(e,t,n,r){return e=ut(7,e,r,t),e.lanes=n,e}function qi(e,t,n,r){return e=ut(22,e,r,t),e.elementType=Kf,e.lanes=n,e.stateNode={isHidden:!1},e}function Vl(e,t,n){return e=ut(6,e,null,t),e.lanes=n,e}function Wl(e,t,n){return t=ut(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Jy(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Cl(0),this.expirationTimes=Cl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Cl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function qu(e,t,n,r,o,s,i,l,a){return e=new Jy(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=ut(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ou(s),e}function e0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Sr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function zm(e){if(!e)return Dn;e=e._reactInternals;e:{if(mr(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(He(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(He(n))return zp(e,n,t)}return t}function Fm(e,t,n,r,o,s,i,l,a){return e=qu(n,r,!0,e,o,s,i,l,a),e.context=zm(null),n=e.current,r=ze(),o=_n(n),s=qt(r,o),s.callback=t??null,Mn(n,s,o),e.current.lanes=o,us(e,o,r),Qe(e,r),e}function Xi(e,t,n,r){var o=t.current,s=ze(),i=_n(o);return n=zm(n),t.context===null?t.context=n:t.pendingContext=n,t=qt(s,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Mn(o,t,i),e!==null&&(Nt(e,o,i,s),Ks(e,o,i)),i}function ki(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Od(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Xu(e,t){Od(e,t),(e=e.alternate)&&Od(e,t)}function t0(){return null}var $m=typeof reportError=="function"?reportError:function(e){console.error(e)};function Zu(e){this._internalRoot=e}Zi.prototype.render=Zu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));Xi(e,t,null,null)};Zi.prototype.unmount=Zu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;dr(function(){Xi(null,e,null,null)}),t[Zt]=null}};function Zi(e){this._internalRoot=e}Zi.prototype.unstable_scheduleHydration=function(e){if(e){var t=vp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<vn.length&&t!==0&&t<vn[n].priority;n++);vn.splice(n,0,e),n===0&&xp(e)}};function Ju(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ji(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function _d(){}function n0(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var u=ki(i);s.call(u)}}var i=Fm(t,r,e,0,null,!1,!1,"",_d);return e._reactRootContainer=i,e[Zt]=i.current,Ko(e.nodeType===8?e.parentNode:e),dr(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=ki(a);l.call(u)}}var a=qu(e,0,!1,null,null,!1,!1,"",_d);return e._reactRootContainer=a,e[Zt]=a.current,Ko(e.nodeType===8?e.parentNode:e),dr(function(){Xi(t,a,n,r)}),a}function el(e,t,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var l=o;o=function(){var a=ki(i);l.call(a)}}Xi(t,i,e,o)}else i=n0(n,t,e,o,r);return ki(i)}hp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Po(t.pendingLanes);n!==0&&(yu(t,n|1),Qe(t,pe()),!(X&6)&&(ro=pe()+500,Wn()))}break;case 13:dr(function(){var r=Jt(e,1);if(r!==null){var o=ze();Nt(r,e,1,o)}}),Xu(e,1)}};xu=function(e){if(e.tag===13){var t=Jt(e,134217728);if(t!==null){var n=ze();Nt(t,e,134217728,n)}Xu(e,134217728)}};gp=function(e){if(e.tag===13){var t=_n(e),n=Jt(e,t);if(n!==null){var r=ze();Nt(n,e,t,r)}Xu(e,t)}};vp=function(){return ee};yp=function(e,t){var n=ee;try{return ee=e,t()}finally{ee=n}};da=function(e,t,n){switch(t){case"input":if(oa(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Wi(r);if(!o)throw Error(R(90));Gf(r),oa(r,o)}}}break;case"textarea":Xf(e,n);break;case"select":t=n.value,t!=null&&_r(e,!!n.multiple,t,!1)}};op=Qu;sp=dr;var r0={usingClientEntryPoint:!1,Events:[ds,Pr,Wi,np,rp,Qu]},So={findFiberByHostInstance:qn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},o0={bundleType:So.bundleType,version:So.version,rendererPackageName:So.rendererPackageName,rendererConfig:So.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:rn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ap(e),e===null?null:e.stateNode},findFiberByHostInstance:So.findFiberByHostInstance||t0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Is=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Is.isDisabled&&Is.supportsFiber)try{$i=Is.inject(o0),Dt=Is}catch{}}tt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=r0;tt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ju(t))throw Error(R(200));return e0(e,t,null,n)};tt.createRoot=function(e,t){if(!Ju(e))throw Error(R(299));var n=!1,r="",o=$m;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=qu(e,1,!1,null,null,n,!1,r,o),e[Zt]=t.current,Ko(e.nodeType===8?e.parentNode:e),new Zu(t)};tt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=ap(t),e=e===null?null:e.stateNode,e};tt.flushSync=function(e){return dr(e)};tt.hydrate=function(e,t,n){if(!Ji(t))throw Error(R(200));return el(null,e,t,!0,n)};tt.hydrateRoot=function(e,t,n){if(!Ju(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=$m;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=Fm(t,null,e,1,n??null,o,!1,s,i),e[Zt]=t.current,Ko(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Zi(t)};tt.render=function(e,t,n){if(!Ji(t))throw Error(R(200));return el(null,e,t,!1,n)};tt.unmountComponentAtNode=function(e){if(!Ji(e))throw Error(R(40));return e._reactRootContainer?(dr(function(){el(null,null,e,!1,function(){e._reactRootContainer=null,e[Zt]=null})}),!0):!1};tt.unstable_batchedUpdates=Qu;tt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ji(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return el(e,t,n,!1,r)};tt.version="18.3.1-next-f1338f8080-20240426";function Bm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Bm)}catch(e){console.error(e)}}Bm(),Bf.exports=tt;var ps=Bf.exports;const Um=jf(ps);var Vm,Ld=ps;Vm=Ld.createRoot,Ld.hydrateRoot;const s0=1,i0=1e6;let Hl=0;function l0(){return Hl=(Hl+1)%Number.MAX_SAFE_INTEGER,Hl.toString()}const Ql=new Map,Id=e=>{if(Ql.has(e))return;const t=setTimeout(()=>{Ql.delete(e),zo({type:"REMOVE_TOAST",toastId:e})},i0);Ql.set(e,t)},a0=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,s0)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?Id(n):e.toasts.forEach(r=>{Id(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},ei=[];let ti={toasts:[]};function zo(e){ti=a0(ti,e),ei.forEach(t=>{t(ti)})}function u0({...e}){const t=l0(),n=o=>zo({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>zo({type:"DISMISS_TOAST",toastId:t});return zo({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function c0(){const[e,t]=w.useState(ti);return w.useEffect(()=>(ei.push(t),()=>{const n=ei.indexOf(t);n>-1&&ei.splice(n,1)}),[e]),{...e,toast:u0,dismiss:n=>zo({type:"DISMISS_TOAST",toastId:n})}}function ye(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Dd(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Wm(...e){return t=>{let n=!1;const r=e.map(o=>{const s=Dd(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():Dd(e[o],null)}}}}function Pt(...e){return w.useCallback(Wm(...e),e)}function tl(e,t=[]){let n=[];function r(s,i){const l=w.createContext(i),a=n.length;n=[...n,i];const u=p=>{var g;const{scope:h,children:f,...b}=p,y=((g=h==null?void 0:h[e])==null?void 0:g[a])||l,x=w.useMemo(()=>b,Object.values(b));return c.jsx(y.Provider,{value:x,children:f})};u.displayName=s+"Provider";function d(p,h){var y;const f=((y=h==null?void 0:h[e])==null?void 0:y[a])||l,b=w.useContext(f);if(b)return b;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return[u,d]}const o=()=>{const s=n.map(i=>w.createContext(i));return function(l){const a=(l==null?void 0:l[e])||s;return w.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,d0(o,...t)]}function d0(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((l,{useScope:a,scopeName:u})=>{const p=a(s)[`__scope${u}`];return{...l,...p}},{});return w.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function Pi(e){const t=p0(e),n=w.forwardRef((r,o)=>{const{children:s,...i}=r,l=w.Children.toArray(s),a=l.find(h0);if(a){const u=a.props.children,d=l.map(p=>p===a?w.Children.count(u)>1?w.Children.only(null):w.isValidElement(u)?u.props.children:null:p);return c.jsx(t,{...i,ref:o,children:w.isValidElement(u)?w.cloneElement(u,void 0,d):null})}return c.jsx(t,{...i,ref:o,children:s})});return n.displayName=`${e}.Slot`,n}var f0=Pi("Slot");function p0(e){const t=w.forwardRef((n,r)=>{const{children:o,...s}=n;if(w.isValidElement(o)){const i=v0(o),l=g0(s,o.props);return o.type!==w.Fragment&&(l.ref=r?Wm(r,i):i),w.cloneElement(o,l)}return w.Children.count(o)>1?w.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Hm=Symbol("radix.slottable");function m0(e){const t=({children:n})=>c.jsx(c.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=Hm,t}function h0(e){return w.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Hm}function g0(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...l)=>{const a=s(...l);return o(...l),a}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function v0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function y0(e){const t=e+"CollectionProvider",[n,r]=tl(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=y=>{const{scope:x,children:g}=y,m=A.useRef(null),v=A.useRef(new Map).current;return c.jsx(o,{scope:x,itemMap:v,collectionRef:m,children:g})};i.displayName=t;const l=e+"CollectionSlot",a=Pi(l),u=A.forwardRef((y,x)=>{const{scope:g,children:m}=y,v=s(l,g),S=Pt(x,v.collectionRef);return c.jsx(a,{ref:S,children:m})});u.displayName=l;const d=e+"CollectionItemSlot",p="data-radix-collection-item",h=Pi(d),f=A.forwardRef((y,x)=>{const{scope:g,children:m,...v}=y,S=A.useRef(null),E=Pt(x,S),C=s(d,g);return A.useEffect(()=>(C.itemMap.set(S,{ref:S,...v}),()=>void C.itemMap.delete(S))),c.jsx(h,{[p]:"",ref:E,children:m})});f.displayName=d;function b(y){const x=s(e+"CollectionConsumer",y);return A.useCallback(()=>{const m=x.collectionRef.current;if(!m)return[];const v=Array.from(m.querySelectorAll(`[${p}]`));return Array.from(x.itemMap.values()).sort((C,N)=>v.indexOf(C.ref.current)-v.indexOf(N.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:i,Slot:u,ItemSlot:f},b,r]}var x0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ye=x0.reduce((e,t)=>{const n=Pi(`Primitive.${t}`),r=w.forwardRef((o,s)=>{const{asChild:i,...l}=o,a=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),c.jsx(a,{...l,ref:s})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Qm(e,t){e&&ps.flushSync(()=>e.dispatchEvent(t))}function zn(e){const t=w.useRef(e);return w.useEffect(()=>{t.current=e}),w.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function w0(e,t=globalThis==null?void 0:globalThis.document){const n=zn(e);w.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var b0="DismissableLayer",Wa="dismissableLayer.update",S0="dismissableLayer.pointerDownOutside",E0="dismissableLayer.focusOutside",zd,Km=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ec=w.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:l,...a}=e,u=w.useContext(Km),[d,p]=w.useState(null),h=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,f]=w.useState({}),b=Pt(t,N=>p(N)),y=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),g=y.indexOf(x),m=d?y.indexOf(d):-1,v=u.layersWithOutsidePointerEventsDisabled.size>0,S=m>=g,E=N0(N=>{const T=N.target,O=[...u.branches].some(M=>M.contains(T));!S||O||(o==null||o(N),i==null||i(N),N.defaultPrevented||l==null||l())},h),C=k0(N=>{const T=N.target;[...u.branches].some(M=>M.contains(T))||(s==null||s(N),i==null||i(N),N.defaultPrevented||l==null||l())},h);return w0(N=>{m===u.layers.size-1&&(r==null||r(N),!N.defaultPrevented&&l&&(N.preventDefault(),l()))},h),w.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(zd=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),Fd(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=zd)}},[d,h,n,u]),w.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),Fd())},[d,u]),w.useEffect(()=>{const N=()=>f({});return document.addEventListener(Wa,N),()=>document.removeEventListener(Wa,N)},[]),c.jsx(Ye.div,{...a,ref:b,style:{pointerEvents:v?S?"auto":"none":void 0,...e.style},onFocusCapture:ye(e.onFocusCapture,C.onFocusCapture),onBlurCapture:ye(e.onBlurCapture,C.onBlurCapture),onPointerDownCapture:ye(e.onPointerDownCapture,E.onPointerDownCapture)})});ec.displayName=b0;var C0="DismissableLayerBranch",Ym=w.forwardRef((e,t)=>{const n=w.useContext(Km),r=w.useRef(null),o=Pt(t,r);return w.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),c.jsx(Ye.div,{...e,ref:o})});Ym.displayName=C0;function N0(e,t=globalThis==null?void 0:globalThis.document){const n=zn(e),r=w.useRef(!1),o=w.useRef(()=>{});return w.useEffect(()=>{const s=l=>{if(l.target&&!r.current){let a=function(){Gm(S0,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function k0(e,t=globalThis==null?void 0:globalThis.document){const n=zn(e),r=w.useRef(!1);return w.useEffect(()=>{const o=s=>{s.target&&!r.current&&Gm(E0,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Fd(){const e=new CustomEvent(Wa);document.dispatchEvent(e)}function Gm(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Qm(o,s):o.dispatchEvent(s)}var P0=ec,j0=Ym,Fn=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},T0="Portal",qm=w.forwardRef((e,t)=>{var l;const{container:n,...r}=e,[o,s]=w.useState(!1);Fn(()=>s(!0),[]);const i=n||o&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return i?Um.createPortal(c.jsx(Ye.div,{...r,ref:t}),i):null});qm.displayName=T0;function R0(e,t){return w.useReducer((n,r)=>t[n][r]??n,e)}var tc=e=>{const{present:t,children:n}=e,r=A0(t),o=typeof n=="function"?n({present:r.isPresent}):w.Children.only(n),s=Pt(r.ref,M0(o));return typeof n=="function"||r.isPresent?w.cloneElement(o,{ref:s}):null};tc.displayName="Presence";function A0(e){const[t,n]=w.useState(),r=w.useRef(null),o=w.useRef(e),s=w.useRef("none"),i=e?"mounted":"unmounted",[l,a]=R0(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const u=Ds(r.current);s.current=l==="mounted"?u:"none"},[l]),Fn(()=>{const u=r.current,d=o.current;if(d!==e){const h=s.current,f=Ds(u);e?a("MOUNT"):f==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(d&&h!==f?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),Fn(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,p=f=>{const y=Ds(r.current).includes(f.animationName);if(f.target===t&&y&&(a("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},h=f=>{f.target===t&&(s.current=Ds(r.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:w.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function Ds(e){return(e==null?void 0:e.animationName)||"none"}function M0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var O0=Ff[" useInsertionEffect ".trim().toString()]||Fn;function _0({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,s,i]=L0({defaultProp:t,onChange:n}),l=e!==void 0,a=l?e:o;{const d=w.useRef(e!==void 0);w.useEffect(()=>{const p=d.current;p!==l&&console.warn(`${r} is changing from ${p?"controlled":"uncontrolled"} to ${l?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),d.current=l},[l,r])}const u=w.useCallback(d=>{var p;if(l){const h=I0(d)?d(e):d;h!==e&&((p=i.current)==null||p.call(i,h))}else s(d)},[l,e,s,i]);return[a,u]}function L0({defaultProp:e,onChange:t}){const[n,r]=w.useState(e),o=w.useRef(n),s=w.useRef(t);return O0(()=>{s.current=t},[t]),w.useEffect(()=>{var i;o.current!==n&&((i=s.current)==null||i.call(s,n),o.current=n)},[n,o]),[n,r,s]}function I0(e){return typeof e=="function"}var D0=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),z0="VisuallyHidden",nl=w.forwardRef((e,t)=>c.jsx(Ye.span,{...e,ref:t,style:{...D0,...e.style}}));nl.displayName=z0;var F0=nl,nc="ToastProvider",[rc,$0,B0]=y0("Toast"),[Xm,Fb]=tl("Toast",[B0]),[U0,rl]=Xm(nc),Zm=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:i}=e,[l,a]=w.useState(null),[u,d]=w.useState(0),p=w.useRef(!1),h=w.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${nc}\`. Expected non-empty \`string\`.`),c.jsx(rc.Provider,{scope:t,children:c.jsx(U0,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:s,toastCount:u,viewport:l,onViewportChange:a,onToastAdd:w.useCallback(()=>d(f=>f+1),[]),onToastRemove:w.useCallback(()=>d(f=>f-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:h,children:i})})};Zm.displayName=nc;var Jm="ToastViewport",V0=["F8"],Ha="toast.viewportPause",Qa="toast.viewportResume",eh=w.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=V0,label:o="Notifications ({hotkey})",...s}=e,i=rl(Jm,n),l=$0(n),a=w.useRef(null),u=w.useRef(null),d=w.useRef(null),p=w.useRef(null),h=Pt(t,p,i.onViewportChange),f=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=i.toastCount>0;w.useEffect(()=>{const x=g=>{var v;r.length!==0&&r.every(S=>g[S]||g.code===S)&&((v=p.current)==null||v.focus())};return document.addEventListener("keydown",x),()=>document.removeEventListener("keydown",x)},[r]),w.useEffect(()=>{const x=a.current,g=p.current;if(b&&x&&g){const m=()=>{if(!i.isClosePausedRef.current){const C=new CustomEvent(Ha);g.dispatchEvent(C),i.isClosePausedRef.current=!0}},v=()=>{if(i.isClosePausedRef.current){const C=new CustomEvent(Qa);g.dispatchEvent(C),i.isClosePausedRef.current=!1}},S=C=>{!x.contains(C.relatedTarget)&&v()},E=()=>{x.contains(document.activeElement)||v()};return x.addEventListener("focusin",m),x.addEventListener("focusout",S),x.addEventListener("pointermove",m),x.addEventListener("pointerleave",E),window.addEventListener("blur",m),window.addEventListener("focus",v),()=>{x.removeEventListener("focusin",m),x.removeEventListener("focusout",S),x.removeEventListener("pointermove",m),x.removeEventListener("pointerleave",E),window.removeEventListener("blur",m),window.removeEventListener("focus",v)}}},[b,i.isClosePausedRef]);const y=w.useCallback(({tabbingDirection:x})=>{const m=l().map(v=>{const S=v.ref.current,E=[S,...nx(S)];return x==="forwards"?E:E.reverse()});return(x==="forwards"?m.reverse():m).flat()},[l]);return w.useEffect(()=>{const x=p.current;if(x){const g=m=>{var E,C,N;const v=m.altKey||m.ctrlKey||m.metaKey;if(m.key==="Tab"&&!v){const T=document.activeElement,O=m.shiftKey;if(m.target===x&&O){(E=u.current)==null||E.focus();return}const D=y({tabbingDirection:O?"backwards":"forwards"}),Q=D.findIndex(_=>_===T);Kl(D.slice(Q+1))?m.preventDefault():O?(C=u.current)==null||C.focus():(N=d.current)==null||N.focus()}};return x.addEventListener("keydown",g),()=>x.removeEventListener("keydown",g)}},[l,y]),c.jsxs(j0,{ref:a,role:"region","aria-label":o.replace("{hotkey}",f),tabIndex:-1,style:{pointerEvents:b?void 0:"none"},children:[b&&c.jsx(Ka,{ref:u,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"forwards"});Kl(x)}}),c.jsx(rc.Slot,{scope:n,children:c.jsx(Ye.ol,{tabIndex:-1,...s,ref:h})}),b&&c.jsx(Ka,{ref:d,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"backwards"});Kl(x)}})]})});eh.displayName=Jm;var th="ToastFocusProxy",Ka=w.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,s=rl(th,n);return c.jsx(nl,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:i=>{var u;const l=i.relatedTarget;!((u=s.viewport)!=null&&u.contains(l))&&r()}})});Ka.displayName=th;var ms="Toast",W0="toast.swipeStart",H0="toast.swipeMove",Q0="toast.swipeCancel",K0="toast.swipeEnd",nh=w.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:s,...i}=e,[l,a]=_0({prop:r,defaultProp:o??!0,onChange:s,caller:ms});return c.jsx(tc,{present:n||l,children:c.jsx(q0,{open:l,...i,ref:t,onClose:()=>a(!1),onPause:zn(e.onPause),onResume:zn(e.onResume),onSwipeStart:ye(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:ye(e.onSwipeMove,u=>{const{x:d,y:p}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${p}px`)}),onSwipeCancel:ye(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:ye(e.onSwipeEnd,u=>{const{x:d,y:p}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${p}px`),a(!1)})})})});nh.displayName=ms;var[Y0,G0]=Xm(ms,{onClose(){}}),q0=w.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:s,onClose:i,onEscapeKeyDown:l,onPause:a,onResume:u,onSwipeStart:d,onSwipeMove:p,onSwipeCancel:h,onSwipeEnd:f,...b}=e,y=rl(ms,n),[x,g]=w.useState(null),m=Pt(t,_=>g(_)),v=w.useRef(null),S=w.useRef(null),E=o||y.duration,C=w.useRef(0),N=w.useRef(E),T=w.useRef(0),{onToastAdd:O,onToastRemove:M}=y,F=zn(()=>{var G;(x==null?void 0:x.contains(document.activeElement))&&((G=y.viewport)==null||G.focus()),i()}),D=w.useCallback(_=>{!_||_===1/0||(window.clearTimeout(T.current),C.current=new Date().getTime(),T.current=window.setTimeout(F,_))},[F]);w.useEffect(()=>{const _=y.viewport;if(_){const G=()=>{D(N.current),u==null||u()},$=()=>{const V=new Date().getTime()-C.current;N.current=N.current-V,window.clearTimeout(T.current),a==null||a()};return _.addEventListener(Ha,$),_.addEventListener(Qa,G),()=>{_.removeEventListener(Ha,$),_.removeEventListener(Qa,G)}}},[y.viewport,E,a,u,D]),w.useEffect(()=>{s&&!y.isClosePausedRef.current&&D(E)},[s,E,y.isClosePausedRef,D]),w.useEffect(()=>(O(),()=>M()),[O,M]);const Q=w.useMemo(()=>x?uh(x):null,[x]);return y.viewport?c.jsxs(c.Fragment,{children:[Q&&c.jsx(X0,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:Q}),c.jsx(Y0,{scope:n,onClose:F,children:ps.createPortal(c.jsx(rc.ItemSlot,{scope:n,children:c.jsx(P0,{asChild:!0,onEscapeKeyDown:ye(l,()=>{y.isFocusedToastEscapeKeyDownRef.current||F(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:c.jsx(Ye.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":y.swipeDirection,...b,ref:m,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:ye(e.onKeyDown,_=>{_.key==="Escape"&&(l==null||l(_.nativeEvent),_.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,F()))}),onPointerDown:ye(e.onPointerDown,_=>{_.button===0&&(v.current={x:_.clientX,y:_.clientY})}),onPointerMove:ye(e.onPointerMove,_=>{if(!v.current)return;const G=_.clientX-v.current.x,$=_.clientY-v.current.y,V=!!S.current,P=["left","right"].includes(y.swipeDirection),j=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,L=P?j(0,G):0,W=P?0:j(0,$),z=_.pointerType==="touch"?10:2,K={x:L,y:W},q={originalEvent:_,delta:K};V?(S.current=K,zs(H0,p,q,{discrete:!1})):$d(K,y.swipeDirection,z)?(S.current=K,zs(W0,d,q,{discrete:!1}),_.target.setPointerCapture(_.pointerId)):(Math.abs(G)>z||Math.abs($)>z)&&(v.current=null)}),onPointerUp:ye(e.onPointerUp,_=>{const G=S.current,$=_.target;if($.hasPointerCapture(_.pointerId)&&$.releasePointerCapture(_.pointerId),S.current=null,v.current=null,G){const V=_.currentTarget,P={originalEvent:_,delta:G};$d(G,y.swipeDirection,y.swipeThreshold)?zs(K0,f,P,{discrete:!0}):zs(Q0,h,P,{discrete:!0}),V.addEventListener("click",j=>j.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),X0=e=>{const{__scopeToast:t,children:n,...r}=e,o=rl(ms,t),[s,i]=w.useState(!1),[l,a]=w.useState(!1);return ex(()=>i(!0)),w.useEffect(()=>{const u=window.setTimeout(()=>a(!0),1e3);return()=>window.clearTimeout(u)},[]),l?null:c.jsx(qm,{asChild:!0,children:c.jsx(nl,{...r,children:s&&c.jsxs(c.Fragment,{children:[o.label," ",n]})})})},Z0="ToastTitle",rh=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return c.jsx(Ye.div,{...r,ref:t})});rh.displayName=Z0;var J0="ToastDescription",oh=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return c.jsx(Ye.div,{...r,ref:t})});oh.displayName=J0;var sh="ToastAction",ih=w.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?c.jsx(ah,{altText:n,asChild:!0,children:c.jsx(oc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${sh}\`. Expected non-empty \`string\`.`),null)});ih.displayName=sh;var lh="ToastClose",oc=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=G0(lh,n);return c.jsx(ah,{asChild:!0,children:c.jsx(Ye.button,{type:"button",...r,ref:t,onClick:ye(e.onClick,o.onClose)})})});oc.displayName=lh;var ah=w.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return c.jsx(Ye.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function uh(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),tx(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",s=r.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...uh(r))}}),t}function zs(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Qm(o,s):o.dispatchEvent(s)}var $d=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return t==="left"||t==="right"?s&&r>n:!s&&o>n};function ex(e=()=>{}){const t=zn(e);Fn(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function tx(e){return e.nodeType===e.ELEMENT_NODE}function nx(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Kl(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var rx=Zm,ch=eh,dh=nh,fh=rh,ph=oh,mh=ih,hh=oc;function gh(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=gh(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function vh(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=gh(e))&&(r&&(r+=" "),r+=t);return r}const Bd=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Ud=vh,sc=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Ud(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(u=>{const d=n==null?void 0:n[u],p=s==null?void 0:s[u];if(d===null)return null;const h=Bd(d)||Bd(p);return o[u][h]}),l=n&&Object.entries(n).reduce((u,d)=>{let[p,h]=d;return h===void 0||(u[p]=h),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,d)=>{let{class:p,className:h,...f}=d;return Object.entries(f).every(b=>{let[y,x]=b;return Array.isArray(x)?x.includes({...s,...l}[y]):{...s,...l}[y]===x})?[...u,p,h]:u},[]);return Ud(e,i,a,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ox=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),yh=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var sx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ix=w.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:s,iconNode:i,...l},a)=>w.createElement("svg",{ref:a,...sx,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:yh("lucide",o),...l},[...i.map(([u,d])=>w.createElement(u,d)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=(e,t)=>{const n=w.forwardRef(({className:r,...o},s)=>w.createElement(ix,{ref:s,iconNode:t,className:yh(`lucide-${ox(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xh=ge("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ji=ge("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lx=ge("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vd=ge("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ti=ge("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ax=ge("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ux=ge("Factory",[["path",{d:"M2 20a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8l-7 5V8l-7 5V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"159hny"}],["path",{d:"M17 18h1",key:"uldtlt"}],["path",{d:"M12 18h1",key:"s9uhes"}],["path",{d:"M7 18h1",key:"1neino"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cx=ge("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dx=ge("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fx=ge("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const px=ge("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mx=ge("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hx=ge("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gx=ge("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ya=ge("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wh=ge("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vx=ge("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yx=ge("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xx=ge("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ri=ge("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),ic="-",wx=e=>{const t=Sx(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const l=i.split(ic);return l[0]===""&&l.length!==1&&l.shift(),bh(l,t)||bx(i)},getConflictingClassGroupIds:(i,l)=>{const a=n[i]||[];return l&&r[i]?[...a,...r[i]]:a}}},bh=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?bh(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(ic);return(i=t.validators.find(({validator:l})=>l(s)))==null?void 0:i.classGroupId},Wd=/^\[(.+)\]$/,bx=e=>{if(Wd.test(e)){const t=Wd.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Sx=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Cx(Object.entries(e.classGroups),n).forEach(([s,i])=>{Ga(i,r,s,t)}),r},Ga=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:Hd(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(Ex(o)){Ga(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{Ga(i,Hd(t,s),n,r)})})},Hd=(e,t)=>{let n=e;return t.split(ic).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Ex=e=>e.isThemeGetter,Cx=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([i,l])=>[t+i,l])):s);return[n,o]}):e,Nx=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},Sh="!",kx=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,i=l=>{const a=[];let u=0,d=0,p;for(let x=0;x<l.length;x++){let g=l[x];if(u===0){if(g===o&&(r||l.slice(x,x+s)===t)){a.push(l.slice(d,x)),d=x+s;continue}if(g==="/"){p=x;continue}}g==="["?u++:g==="]"&&u--}const h=a.length===0?l:l.substring(d),f=h.startsWith(Sh),b=f?h.substring(1):h,y=p&&p>d?p-d:void 0;return{modifiers:a,hasImportantModifier:f,baseClassName:b,maybePostfixModifierPosition:y}};return n?l=>n({className:l,parseClassName:i}):i},Px=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},jx=e=>({cache:Nx(e.cacheSize),parseClassName:kx(e),...wx(e)}),Tx=/\s+/,Rx=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],i=e.trim().split(Tx);let l="";for(let a=i.length-1;a>=0;a-=1){const u=i[a],{modifiers:d,hasImportantModifier:p,baseClassName:h,maybePostfixModifierPosition:f}=n(u);let b=!!f,y=r(b?h.substring(0,f):h);if(!y){if(!b){l=u+(l.length>0?" "+l:l);continue}if(y=r(h),!y){l=u+(l.length>0?" "+l:l);continue}b=!1}const x=Px(d).join(":"),g=p?x+Sh:x,m=g+y;if(s.includes(m))continue;s.push(m);const v=o(y,b);for(let S=0;S<v.length;++S){const E=v[S];s.push(g+E)}l=u+(l.length>0?" "+l:l)}return l};function Ax(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Eh(t))&&(r&&(r+=" "),r+=n);return r}const Eh=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Eh(e[r]))&&(n&&(n+=" "),n+=t);return n};function Mx(e,...t){let n,r,o,s=i;function i(a){const u=t.reduce((d,p)=>p(d),e());return n=jx(u),r=n.cache.get,o=n.cache.set,s=l,l(a)}function l(a){const u=r(a);if(u)return u;const d=Rx(a,n);return o(a,d),d}return function(){return s(Ax.apply(null,arguments))}}const re=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Ch=/^\[(?:([a-z-]+):)?(.+)\]$/i,Ox=/^\d+\/\d+$/,_x=new Set(["px","full","screen"]),Lx=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ix=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Dx=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,zx=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Fx=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ut=e=>$r(e)||_x.has(e)||Ox.test(e),fn=e=>uo(e,"length",Kx),$r=e=>!!e&&!Number.isNaN(Number(e)),Yl=e=>uo(e,"number",$r),Eo=e=>!!e&&Number.isInteger(Number(e)),$x=e=>e.endsWith("%")&&$r(e.slice(0,-1)),H=e=>Ch.test(e),pn=e=>Lx.test(e),Bx=new Set(["length","size","percentage"]),Ux=e=>uo(e,Bx,Nh),Vx=e=>uo(e,"position",Nh),Wx=new Set(["image","url"]),Hx=e=>uo(e,Wx,Gx),Qx=e=>uo(e,"",Yx),Co=()=>!0,uo=(e,t,n)=>{const r=Ch.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},Kx=e=>Ix.test(e)&&!Dx.test(e),Nh=()=>!1,Yx=e=>zx.test(e),Gx=e=>Fx.test(e),qx=()=>{const e=re("colors"),t=re("spacing"),n=re("blur"),r=re("brightness"),o=re("borderColor"),s=re("borderRadius"),i=re("borderSpacing"),l=re("borderWidth"),a=re("contrast"),u=re("grayscale"),d=re("hueRotate"),p=re("invert"),h=re("gap"),f=re("gradientColorStops"),b=re("gradientColorStopPositions"),y=re("inset"),x=re("margin"),g=re("opacity"),m=re("padding"),v=re("saturate"),S=re("scale"),E=re("sepia"),C=re("skew"),N=re("space"),T=re("translate"),O=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto",H,t],D=()=>[H,t],Q=()=>["",Ut,fn],_=()=>["auto",$r,H],G=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],$=()=>["solid","dashed","dotted","double","none"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],P=()=>["start","end","center","between","around","evenly","stretch"],j=()=>["","0",H],L=()=>["auto","avoid","all","avoid-page","page","left","right","column"],W=()=>[$r,H];return{cacheSize:500,separator:":",theme:{colors:[Co],spacing:[Ut,fn],blur:["none","",pn,H],brightness:W(),borderColor:[e],borderRadius:["none","","full",pn,H],borderSpacing:D(),borderWidth:Q(),contrast:W(),grayscale:j(),hueRotate:W(),invert:j(),gap:D(),gradientColorStops:[e],gradientColorStopPositions:[$x,fn],inset:F(),margin:F(),opacity:W(),padding:D(),saturate:W(),scale:W(),sepia:j(),skew:W(),space:D(),translate:D()},classGroups:{aspect:[{aspect:["auto","square","video",H]}],container:["container"],columns:[{columns:[pn]}],"break-after":[{"break-after":L()}],"break-before":[{"break-before":L()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...G(),H]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Eo,H]}],basis:[{basis:F()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",H]}],grow:[{grow:j()}],shrink:[{shrink:j()}],order:[{order:["first","last","none",Eo,H]}],"grid-cols":[{"grid-cols":[Co]}],"col-start-end":[{col:["auto",{span:["full",Eo,H]},H]}],"col-start":[{"col-start":_()}],"col-end":[{"col-end":_()}],"grid-rows":[{"grid-rows":[Co]}],"row-start-end":[{row:["auto",{span:[Eo,H]},H]}],"row-start":[{"row-start":_()}],"row-end":[{"row-end":_()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",H]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",H]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...P()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...P(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...P(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[m]}],px:[{px:[m]}],py:[{py:[m]}],ps:[{ps:[m]}],pe:[{pe:[m]}],pt:[{pt:[m]}],pr:[{pr:[m]}],pb:[{pb:[m]}],pl:[{pl:[m]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",H,t]}],"min-w":[{"min-w":[H,t,"min","max","fit"]}],"max-w":[{"max-w":[H,t,"none","full","min","max","fit","prose",{screen:[pn]},pn]}],h:[{h:[H,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[H,t,"auto","min","max","fit"]}],"font-size":[{text:["base",pn,fn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Yl]}],"font-family":[{font:[Co]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",H]}],"line-clamp":[{"line-clamp":["none",$r,Yl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ut,H]}],"list-image":[{"list-image":["none",H]}],"list-style-type":[{list:["none","disc","decimal",H]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ut,fn]}],"underline-offset":[{"underline-offset":["auto",Ut,H]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:D()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...G(),Vx]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Ux]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Hx]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[b]}],"gradient-via-pos":[{via:[b]}],"gradient-to-pos":[{to:[b]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...$(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:$()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...$()]}],"outline-offset":[{"outline-offset":[Ut,H]}],"outline-w":[{outline:[Ut,fn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:Q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[Ut,fn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",pn,Qx]}],"shadow-color":[{shadow:[Co]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...V(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":V()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",pn,H]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[p]}],saturate:[{saturate:[v]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",H]}],duration:[{duration:W()}],ease:[{ease:["linear","in","out","in-out",H]}],delay:[{delay:W()}],animate:[{animate:["none","spin","ping","pulse","bounce",H]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[Eo,H]}],"translate-x":[{"translate-x":[T]}],"translate-y":[{"translate-y":[T]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",H]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":D()}],"scroll-mx":[{"scroll-mx":D()}],"scroll-my":[{"scroll-my":D()}],"scroll-ms":[{"scroll-ms":D()}],"scroll-me":[{"scroll-me":D()}],"scroll-mt":[{"scroll-mt":D()}],"scroll-mr":[{"scroll-mr":D()}],"scroll-mb":[{"scroll-mb":D()}],"scroll-ml":[{"scroll-ml":D()}],"scroll-p":[{"scroll-p":D()}],"scroll-px":[{"scroll-px":D()}],"scroll-py":[{"scroll-py":D()}],"scroll-ps":[{"scroll-ps":D()}],"scroll-pe":[{"scroll-pe":D()}],"scroll-pt":[{"scroll-pt":D()}],"scroll-pr":[{"scroll-pr":D()}],"scroll-pb":[{"scroll-pb":D()}],"scroll-pl":[{"scroll-pl":D()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Ut,fn,Yl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Xx=Mx(qx);function _e(...e){return Xx(vh(e))}const Zx=rx,kh=w.forwardRef(({className:e,...t},n)=>c.jsx(ch,{ref:n,className:_e("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));kh.displayName=ch.displayName;const Jx=sc("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Ph=w.forwardRef(({className:e,variant:t,...n},r)=>c.jsx(dh,{ref:r,className:_e(Jx({variant:t}),e),...n}));Ph.displayName=dh.displayName;const ew=w.forwardRef(({className:e,...t},n)=>c.jsx(mh,{ref:n,className:_e("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));ew.displayName=mh.displayName;const jh=w.forwardRef(({className:e,...t},n)=>c.jsx(hh,{ref:n,className:_e("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:c.jsx(xx,{className:"h-4 w-4"})}));jh.displayName=hh.displayName;const Th=w.forwardRef(({className:e,...t},n)=>c.jsx(fh,{ref:n,className:_e("text-sm font-semibold",e),...t}));Th.displayName=fh.displayName;const Rh=w.forwardRef(({className:e,...t},n)=>c.jsx(ph,{ref:n,className:_e("text-sm opacity-90",e),...t}));Rh.displayName=ph.displayName;function tw(){const{toasts:e}=c0();return c.jsxs(Zx,{children:[e.map(function({id:t,title:n,description:r,action:o,...s}){return c.jsxs(Ph,{...s,children:[c.jsxs("div",{className:"grid gap-1",children:[n&&c.jsx(Th,{children:n}),r&&c.jsx(Rh,{children:r})]}),o,c.jsx(jh,{})]},t)}),c.jsx(kh,{})]})}var nw=e=>{switch(e){case"success":return sw;case"info":return lw;case"warning":return iw;case"error":return aw;default:return null}},rw=Array(12).fill(0),ow=({visible:e,className:t})=>A.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},A.createElement("div",{className:"sonner-spinner"},rw.map((n,r)=>A.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${r}`})))),sw=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),iw=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),lw=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),aw=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),uw=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},A.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),A.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),cw=()=>{let[e,t]=A.useState(document.hidden);return A.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},qa=1,dw=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:qa++,s=this.toasts.find(l=>l.id===o),i=e.dismissible===void 0?!0:e.dismissible;return this.dismissedToasts.has(o)&&this.dismissedToasts.delete(o),s?this.toasts=this.toasts.map(l=>l.id===o?(this.publish({...l,...e,id:o,title:n}),{...l,...e,id:o,dismissible:i,title:n}):l):this.addToast({title:n,...r,dismissible:i,id:o}),o},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0,s,i=r.then(async a=>{if(s=["resolve",a],A.isValidElement(a))o=!1,this.create({id:n,type:"default",message:a});else if(pw(a)&&!a.ok){o=!1;let u=typeof t.error=="function"?await t.error(`HTTP error! status: ${a.status}`):t.error,d=typeof t.description=="function"?await t.description(`HTTP error! status: ${a.status}`):t.description;this.create({id:n,type:"error",message:u,description:d})}else if(t.success!==void 0){o=!1;let u=typeof t.success=="function"?await t.success(a):t.success,d=typeof t.description=="function"?await t.description(a):t.description;this.create({id:n,type:"success",message:u,description:d})}}).catch(async a=>{if(s=["reject",a],t.error!==void 0){o=!1;let u=typeof t.error=="function"?await t.error(a):t.error,d=typeof t.description=="function"?await t.description(a):t.description;this.create({id:n,type:"error",message:u,description:d})}}).finally(()=>{var a;o&&(this.dismiss(n),n=void 0),(a=t.finally)==null||a.call(t)}),l=()=>new Promise((a,u)=>i.then(()=>s[0]==="reject"?u(s[1]):a(s[1])).catch(u));return typeof n!="string"&&typeof n!="number"?{unwrap:l}:Object.assign(n,{unwrap:l})},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||qa++;return this.create({jsx:e(n),id:n,...t}),n},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},Be=new dw,fw=(e,t)=>{let n=(t==null?void 0:t.id)||qa++;return Be.addToast({title:e,...t,id:n}),n},pw=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",mw=fw,hw=()=>Be.toasts,gw=()=>Be.getActiveToasts();Object.assign(mw,{success:Be.success,info:Be.info,warning:Be.warning,error:Be.error,custom:Be.custom,message:Be.message,promise:Be.promise,dismiss:Be.dismiss,loading:Be.loading},{getHistory:hw,getToasts:gw});function vw(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}vw(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Fs(e){return e.label!==void 0}var yw=3,xw="32px",ww="16px",Qd=4e3,bw=356,Sw=14,Ew=20,Cw=200;function vt(...e){return e.filter(Boolean).join(" ")}function Nw(e){let[t,n]=e.split("-"),r=[];return t&&r.push(t),n&&r.push(n),r}var kw=e=>{var t,n,r,o,s,i,l,a,u,d,p;let{invert:h,toast:f,unstyled:b,interacting:y,setHeights:x,visibleToasts:g,heights:m,index:v,toasts:S,expanded:E,removeToast:C,defaultRichColors:N,closeButton:T,style:O,cancelButtonStyle:M,actionButtonStyle:F,className:D="",descriptionClassName:Q="",duration:_,position:G,gap:$,loadingIcon:V,expandByDefault:P,classNames:j,icons:L,closeButtonAriaLabel:W="Close toast",pauseWhenPageIsHidden:z}=e,[K,q]=A.useState(null),[me,Ne]=A.useState(null),[J,hr]=A.useState(!1),[on,Hn]=A.useState(!1),[sn,gr]=A.useState(!1),[ln,gs]=A.useState(!1),[ml,vs]=A.useState(!1),[hl,po]=A.useState(0),[vr,wc]=A.useState(0),mo=A.useRef(f.duration||_||Qd),bc=A.useRef(null),Qn=A.useRef(null),Cg=v===0,Ng=v+1<=g,rt=f.type,yr=f.dismissible!==!1,kg=f.className||"",Pg=f.descriptionClassName||"",ys=A.useMemo(()=>m.findIndex(B=>B.toastId===f.id)||0,[m,f.id]),jg=A.useMemo(()=>{var B;return(B=f.closeButton)!=null?B:T},[f.closeButton,T]),Sc=A.useMemo(()=>f.duration||_||Qd,[f.duration,_]),gl=A.useRef(0),xr=A.useRef(0),Ec=A.useRef(0),wr=A.useRef(null),[Tg,Rg]=G.split("-"),Cc=A.useMemo(()=>m.reduce((B,te,ie)=>ie>=ys?B:B+te.height,0),[m,ys]),Nc=cw(),Ag=f.invert||h,vl=rt==="loading";xr.current=A.useMemo(()=>ys*$+Cc,[ys,Cc]),A.useEffect(()=>{mo.current=Sc},[Sc]),A.useEffect(()=>{hr(!0)},[]),A.useEffect(()=>{let B=Qn.current;if(B){let te=B.getBoundingClientRect().height;return wc(te),x(ie=>[{toastId:f.id,height:te,position:f.position},...ie]),()=>x(ie=>ie.filter(pt=>pt.toastId!==f.id))}},[x,f.id]),A.useLayoutEffect(()=>{if(!J)return;let B=Qn.current,te=B.style.height;B.style.height="auto";let ie=B.getBoundingClientRect().height;B.style.height=te,wc(ie),x(pt=>pt.find(mt=>mt.toastId===f.id)?pt.map(mt=>mt.toastId===f.id?{...mt,height:ie}:mt):[{toastId:f.id,height:ie,position:f.position},...pt])},[J,f.title,f.description,x,f.id]);let an=A.useCallback(()=>{Hn(!0),po(xr.current),x(B=>B.filter(te=>te.toastId!==f.id)),setTimeout(()=>{C(f)},Cw)},[f,C,x,xr]);A.useEffect(()=>{if(f.promise&&rt==="loading"||f.duration===1/0||f.type==="loading")return;let B;return E||y||z&&Nc?(()=>{if(Ec.current<gl.current){let te=new Date().getTime()-gl.current;mo.current=mo.current-te}Ec.current=new Date().getTime()})():mo.current!==1/0&&(gl.current=new Date().getTime(),B=setTimeout(()=>{var te;(te=f.onAutoClose)==null||te.call(f,f),an()},mo.current)),()=>clearTimeout(B)},[E,y,f,rt,z,Nc,an]),A.useEffect(()=>{f.delete&&an()},[an,f.delete]);function Mg(){var B,te,ie;return L!=null&&L.loading?A.createElement("div",{className:vt(j==null?void 0:j.loader,(B=f==null?void 0:f.classNames)==null?void 0:B.loader,"sonner-loader"),"data-visible":rt==="loading"},L.loading):V?A.createElement("div",{className:vt(j==null?void 0:j.loader,(te=f==null?void 0:f.classNames)==null?void 0:te.loader,"sonner-loader"),"data-visible":rt==="loading"},V):A.createElement(ow,{className:vt(j==null?void 0:j.loader,(ie=f==null?void 0:f.classNames)==null?void 0:ie.loader),visible:rt==="loading"})}return A.createElement("li",{tabIndex:0,ref:Qn,className:vt(D,kg,j==null?void 0:j.toast,(t=f==null?void 0:f.classNames)==null?void 0:t.toast,j==null?void 0:j.default,j==null?void 0:j[rt],(n=f==null?void 0:f.classNames)==null?void 0:n[rt]),"data-sonner-toast":"","data-rich-colors":(r=f.richColors)!=null?r:N,"data-styled":!(f.jsx||f.unstyled||b),"data-mounted":J,"data-promise":!!f.promise,"data-swiped":ml,"data-removed":on,"data-visible":Ng,"data-y-position":Tg,"data-x-position":Rg,"data-index":v,"data-front":Cg,"data-swiping":sn,"data-dismissible":yr,"data-type":rt,"data-invert":Ag,"data-swipe-out":ln,"data-swipe-direction":me,"data-expanded":!!(E||P&&J),style:{"--index":v,"--toasts-before":v,"--z-index":S.length-v,"--offset":`${on?hl:xr.current}px`,"--initial-height":P?"auto":`${vr}px`,...O,...f.style},onDragEnd:()=>{gr(!1),q(null),wr.current=null},onPointerDown:B=>{vl||!yr||(bc.current=new Date,po(xr.current),B.target.setPointerCapture(B.pointerId),B.target.tagName!=="BUTTON"&&(gr(!0),wr.current={x:B.clientX,y:B.clientY}))},onPointerUp:()=>{var B,te,ie,pt;if(ln||!yr)return;wr.current=null;let mt=Number(((B=Qn.current)==null?void 0:B.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),un=Number(((te=Qn.current)==null?void 0:te.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),Kn=new Date().getTime()-((ie=bc.current)==null?void 0:ie.getTime()),ht=K==="x"?mt:un,cn=Math.abs(ht)/Kn;if(Math.abs(ht)>=Ew||cn>.11){po(xr.current),(pt=f.onDismiss)==null||pt.call(f,f),Ne(K==="x"?mt>0?"right":"left":un>0?"down":"up"),an(),gs(!0),vs(!1);return}gr(!1),q(null)},onPointerMove:B=>{var te,ie,pt,mt;if(!wr.current||!yr||((te=window.getSelection())==null?void 0:te.toString().length)>0)return;let un=B.clientY-wr.current.y,Kn=B.clientX-wr.current.x,ht=(ie=e.swipeDirections)!=null?ie:Nw(G);!K&&(Math.abs(Kn)>1||Math.abs(un)>1)&&q(Math.abs(Kn)>Math.abs(un)?"x":"y");let cn={x:0,y:0};K==="y"?(ht.includes("top")||ht.includes("bottom"))&&(ht.includes("top")&&un<0||ht.includes("bottom")&&un>0)&&(cn.y=un):K==="x"&&(ht.includes("left")||ht.includes("right"))&&(ht.includes("left")&&Kn<0||ht.includes("right")&&Kn>0)&&(cn.x=Kn),(Math.abs(cn.x)>0||Math.abs(cn.y)>0)&&vs(!0),(pt=Qn.current)==null||pt.style.setProperty("--swipe-amount-x",`${cn.x}px`),(mt=Qn.current)==null||mt.style.setProperty("--swipe-amount-y",`${cn.y}px`)}},jg&&!f.jsx?A.createElement("button",{"aria-label":W,"data-disabled":vl,"data-close-button":!0,onClick:vl||!yr?()=>{}:()=>{var B;an(),(B=f.onDismiss)==null||B.call(f,f)},className:vt(j==null?void 0:j.closeButton,(o=f==null?void 0:f.classNames)==null?void 0:o.closeButton)},(s=L==null?void 0:L.close)!=null?s:uw):null,f.jsx||w.isValidElement(f.title)?f.jsx?f.jsx:typeof f.title=="function"?f.title():f.title:A.createElement(A.Fragment,null,rt||f.icon||f.promise?A.createElement("div",{"data-icon":"",className:vt(j==null?void 0:j.icon,(i=f==null?void 0:f.classNames)==null?void 0:i.icon)},f.promise||f.type==="loading"&&!f.icon?f.icon||Mg():null,f.type!=="loading"?f.icon||(L==null?void 0:L[rt])||nw(rt):null):null,A.createElement("div",{"data-content":"",className:vt(j==null?void 0:j.content,(l=f==null?void 0:f.classNames)==null?void 0:l.content)},A.createElement("div",{"data-title":"",className:vt(j==null?void 0:j.title,(a=f==null?void 0:f.classNames)==null?void 0:a.title)},typeof f.title=="function"?f.title():f.title),f.description?A.createElement("div",{"data-description":"",className:vt(Q,Pg,j==null?void 0:j.description,(u=f==null?void 0:f.classNames)==null?void 0:u.description)},typeof f.description=="function"?f.description():f.description):null),w.isValidElement(f.cancel)?f.cancel:f.cancel&&Fs(f.cancel)?A.createElement("button",{"data-button":!0,"data-cancel":!0,style:f.cancelButtonStyle||M,onClick:B=>{var te,ie;Fs(f.cancel)&&yr&&((ie=(te=f.cancel).onClick)==null||ie.call(te,B),an())},className:vt(j==null?void 0:j.cancelButton,(d=f==null?void 0:f.classNames)==null?void 0:d.cancelButton)},f.cancel.label):null,w.isValidElement(f.action)?f.action:f.action&&Fs(f.action)?A.createElement("button",{"data-button":!0,"data-action":!0,style:f.actionButtonStyle||F,onClick:B=>{var te,ie;Fs(f.action)&&((ie=(te=f.action).onClick)==null||ie.call(te,B),!B.defaultPrevented&&an())},className:vt(j==null?void 0:j.actionButton,(p=f==null?void 0:f.classNames)==null?void 0:p.actionButton)},f.action.label):null))};function Kd(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}function Pw(e,t){let n={};return[e,t].forEach((r,o)=>{let s=o===1,i=s?"--mobile-offset":"--offset",l=s?ww:xw;function a(u){["top","right","bottom","left"].forEach(d=>{n[`${i}-${d}`]=typeof u=="number"?`${u}px`:u})}typeof r=="number"||typeof r=="string"?a(r):typeof r=="object"?["top","right","bottom","left"].forEach(u=>{r[u]===void 0?n[`${i}-${u}`]=l:n[`${i}-${u}`]=typeof r[u]=="number"?`${r[u]}px`:r[u]}):a(l)}),n}var jw=w.forwardRef(function(e,t){let{invert:n,position:r="bottom-right",hotkey:o=["altKey","KeyT"],expand:s,closeButton:i,className:l,offset:a,mobileOffset:u,theme:d="light",richColors:p,duration:h,style:f,visibleToasts:b=yw,toastOptions:y,dir:x=Kd(),gap:g=Sw,loadingIcon:m,icons:v,containerAriaLabel:S="Notifications",pauseWhenPageIsHidden:E}=e,[C,N]=A.useState([]),T=A.useMemo(()=>Array.from(new Set([r].concat(C.filter(z=>z.position).map(z=>z.position)))),[C,r]),[O,M]=A.useState([]),[F,D]=A.useState(!1),[Q,_]=A.useState(!1),[G,$]=A.useState(d!=="system"?d:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),V=A.useRef(null),P=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),j=A.useRef(null),L=A.useRef(!1),W=A.useCallback(z=>{N(K=>{var q;return(q=K.find(me=>me.id===z.id))!=null&&q.delete||Be.dismiss(z.id),K.filter(({id:me})=>me!==z.id)})},[]);return A.useEffect(()=>Be.subscribe(z=>{if(z.dismiss){N(K=>K.map(q=>q.id===z.id?{...q,delete:!0}:q));return}setTimeout(()=>{Um.flushSync(()=>{N(K=>{let q=K.findIndex(me=>me.id===z.id);return q!==-1?[...K.slice(0,q),{...K[q],...z},...K.slice(q+1)]:[z,...K]})})})}),[]),A.useEffect(()=>{if(d!=="system"){$(d);return}if(d==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?$("dark"):$("light")),typeof window>"u")return;let z=window.matchMedia("(prefers-color-scheme: dark)");try{z.addEventListener("change",({matches:K})=>{$(K?"dark":"light")})}catch{z.addListener(({matches:q})=>{try{$(q?"dark":"light")}catch(me){console.error(me)}})}},[d]),A.useEffect(()=>{C.length<=1&&D(!1)},[C]),A.useEffect(()=>{let z=K=>{var q,me;o.every(Ne=>K[Ne]||K.code===Ne)&&(D(!0),(q=V.current)==null||q.focus()),K.code==="Escape"&&(document.activeElement===V.current||(me=V.current)!=null&&me.contains(document.activeElement))&&D(!1)};return document.addEventListener("keydown",z),()=>document.removeEventListener("keydown",z)},[o]),A.useEffect(()=>{if(V.current)return()=>{j.current&&(j.current.focus({preventScroll:!0}),j.current=null,L.current=!1)}},[V.current]),A.createElement("section",{ref:t,"aria-label":`${S} ${P}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},T.map((z,K)=>{var q;let[me,Ne]=z.split("-");return C.length?A.createElement("ol",{key:z,dir:x==="auto"?Kd():x,tabIndex:-1,ref:V,className:l,"data-sonner-toaster":!0,"data-theme":G,"data-y-position":me,"data-lifted":F&&C.length>1&&!s,"data-x-position":Ne,style:{"--front-toast-height":`${((q=O[0])==null?void 0:q.height)||0}px`,"--width":`${bw}px`,"--gap":`${g}px`,...f,...Pw(a,u)},onBlur:J=>{L.current&&!J.currentTarget.contains(J.relatedTarget)&&(L.current=!1,j.current&&(j.current.focus({preventScroll:!0}),j.current=null))},onFocus:J=>{J.target instanceof HTMLElement&&J.target.dataset.dismissible==="false"||L.current||(L.current=!0,j.current=J.relatedTarget)},onMouseEnter:()=>D(!0),onMouseMove:()=>D(!0),onMouseLeave:()=>{Q||D(!1)},onDragEnd:()=>D(!1),onPointerDown:J=>{J.target instanceof HTMLElement&&J.target.dataset.dismissible==="false"||_(!0)},onPointerUp:()=>_(!1)},C.filter(J=>!J.position&&K===0||J.position===z).map((J,hr)=>{var on,Hn;return A.createElement(kw,{key:J.id,icons:v,index:hr,toast:J,defaultRichColors:p,duration:(on=y==null?void 0:y.duration)!=null?on:h,className:y==null?void 0:y.className,descriptionClassName:y==null?void 0:y.descriptionClassName,invert:n,visibleToasts:b,closeButton:(Hn=y==null?void 0:y.closeButton)!=null?Hn:i,interacting:Q,position:z,style:y==null?void 0:y.style,unstyled:y==null?void 0:y.unstyled,classNames:y==null?void 0:y.classNames,cancelButtonStyle:y==null?void 0:y.cancelButtonStyle,actionButtonStyle:y==null?void 0:y.actionButtonStyle,removeToast:W,toasts:C.filter(sn=>sn.position==J.position),heights:O.filter(sn=>sn.position==J.position),setHeights:M,expandByDefault:s,gap:g,loadingIcon:m,expanded:F,pauseWhenPageIsHidden:E,swipeDirections:e.swipeDirections})})):null}))});const Tw=["top","right","bottom","left"],$n=Math.min,qe=Math.max,Ai=Math.round,$s=Math.floor,Ft=e=>({x:e,y:e}),Rw={left:"right",right:"left",bottom:"top",top:"bottom"},Aw={start:"end",end:"start"};function Xa(e,t,n){return qe(e,$n(t,n))}function tn(e,t){return typeof e=="function"?e(t):e}function nn(e){return e.split("-")[0]}function co(e){return e.split("-")[1]}function lc(e){return e==="x"?"y":"x"}function ac(e){return e==="y"?"height":"width"}const Mw=new Set(["top","bottom"]);function It(e){return Mw.has(nn(e))?"y":"x"}function uc(e){return lc(It(e))}function Ow(e,t,n){n===void 0&&(n=!1);const r=co(e),o=uc(e),s=ac(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Mi(i)),[i,Mi(i)]}function _w(e){const t=Mi(e);return[Za(e),t,Za(t)]}function Za(e){return e.replace(/start|end/g,t=>Aw[t])}const Yd=["left","right"],Gd=["right","left"],Lw=["top","bottom"],Iw=["bottom","top"];function Dw(e,t,n){switch(e){case"top":case"bottom":return n?t?Gd:Yd:t?Yd:Gd;case"left":case"right":return t?Lw:Iw;default:return[]}}function zw(e,t,n,r){const o=co(e);let s=Dw(nn(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(Za)))),s}function Mi(e){return e.replace(/left|right|bottom|top/g,t=>Rw[t])}function Fw(e){return{top:0,right:0,bottom:0,left:0,...e}}function Ah(e){return typeof e!="number"?Fw(e):{top:e,right:e,bottom:e,left:e}}function Oi(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function qd(e,t,n){let{reference:r,floating:o}=e;const s=It(t),i=uc(t),l=ac(i),a=nn(t),u=s==="y",d=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,h=r[l]/2-o[l]/2;let f;switch(a){case"top":f={x:d,y:r.y-o.height};break;case"bottom":f={x:d,y:r.y+r.height};break;case"right":f={x:r.x+r.width,y:p};break;case"left":f={x:r.x-o.width,y:p};break;default:f={x:r.x,y:r.y}}switch(co(t)){case"start":f[i]-=h*(n&&u?-1:1);break;case"end":f[i]+=h*(n&&u?-1:1);break}return f}const $w=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,l=s.filter(Boolean),a=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:p}=qd(u,r,a),h=r,f={},b=0;for(let y=0;y<l.length;y++){const{name:x,fn:g}=l[y],{x:m,y:v,data:S,reset:E}=await g({x:d,y:p,initialPlacement:r,placement:h,strategy:o,middlewareData:f,rects:u,platform:i,elements:{reference:e,floating:t}});d=m??d,p=v??p,f={...f,[x]:{...f[x],...S}},E&&b<=50&&(b++,typeof E=="object"&&(E.placement&&(h=E.placement),E.rects&&(u=E.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:d,y:p}=qd(u,h,a)),y=-1)}return{x:d,y:p,placement:h,strategy:o,middlewareData:f}};async function ns(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:l,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:p="floating",altBoundary:h=!1,padding:f=0}=tn(t,e),b=Ah(f),x=l[h?p==="floating"?"reference":"floating":p],g=Oi(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(x)))==null||n?x:x.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(l.floating)),boundary:u,rootBoundary:d,strategy:a})),m=p==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,v=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l.floating)),S=await(s.isElement==null?void 0:s.isElement(v))?await(s.getScale==null?void 0:s.getScale(v))||{x:1,y:1}:{x:1,y:1},E=Oi(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:m,offsetParent:v,strategy:a}):m);return{top:(g.top-E.top+b.top)/S.y,bottom:(E.bottom-g.bottom+b.bottom)/S.y,left:(g.left-E.left+b.left)/S.x,right:(E.right-g.right+b.right)/S.x}}const Bw=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:l,middlewareData:a}=t,{element:u,padding:d=0}=tn(e,t)||{};if(u==null)return{};const p=Ah(d),h={x:n,y:r},f=uc(o),b=ac(f),y=await i.getDimensions(u),x=f==="y",g=x?"top":"left",m=x?"bottom":"right",v=x?"clientHeight":"clientWidth",S=s.reference[b]+s.reference[f]-h[f]-s.floating[b],E=h[f]-s.reference[f],C=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let N=C?C[v]:0;(!N||!await(i.isElement==null?void 0:i.isElement(C)))&&(N=l.floating[v]||s.floating[b]);const T=S/2-E/2,O=N/2-y[b]/2-1,M=$n(p[g],O),F=$n(p[m],O),D=M,Q=N-y[b]-F,_=N/2-y[b]/2+T,G=Xa(D,_,Q),$=!a.arrow&&co(o)!=null&&_!==G&&s.reference[b]/2-(_<D?M:F)-y[b]/2<0,V=$?_<D?_-D:_-Q:0;return{[f]:h[f]+V,data:{[f]:G,centerOffset:_-G-V,...$&&{alignmentOffset:V}},reset:$}}}),Uw=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:l,platform:a,elements:u}=t,{mainAxis:d=!0,crossAxis:p=!0,fallbackPlacements:h,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:y=!0,...x}=tn(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const g=nn(o),m=It(l),v=nn(l)===l,S=await(a.isRTL==null?void 0:a.isRTL(u.floating)),E=h||(v||!y?[Mi(l)]:_w(l)),C=b!=="none";!h&&C&&E.push(...zw(l,y,b,S));const N=[l,...E],T=await ns(t,x),O=[];let M=((r=s.flip)==null?void 0:r.overflows)||[];if(d&&O.push(T[g]),p){const _=Ow(o,i,S);O.push(T[_[0]],T[_[1]])}if(M=[...M,{placement:o,overflows:O}],!O.every(_=>_<=0)){var F,D;const _=(((F=s.flip)==null?void 0:F.index)||0)+1,G=N[_];if(G&&(!(p==="alignment"?m!==It(G):!1)||M.every(P=>P.overflows[0]>0&&It(P.placement)===m)))return{data:{index:_,overflows:M},reset:{placement:G}};let $=(D=M.filter(V=>V.overflows[0]<=0).sort((V,P)=>V.overflows[1]-P.overflows[1])[0])==null?void 0:D.placement;if(!$)switch(f){case"bestFit":{var Q;const V=(Q=M.filter(P=>{if(C){const j=It(P.placement);return j===m||j==="y"}return!0}).map(P=>[P.placement,P.overflows.filter(j=>j>0).reduce((j,L)=>j+L,0)]).sort((P,j)=>P[1]-j[1])[0])==null?void 0:Q[0];V&&($=V);break}case"initialPlacement":$=l;break}if(o!==$)return{reset:{placement:$}}}return{}}}};function Xd(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Zd(e){return Tw.some(t=>e[t]>=0)}const Vw=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=tn(e,t);switch(r){case"referenceHidden":{const s=await ns(t,{...o,elementContext:"reference"}),i=Xd(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Zd(i)}}}case"escaped":{const s=await ns(t,{...o,altBoundary:!0}),i=Xd(s,n.floating);return{data:{escapedOffsets:i,escaped:Zd(i)}}}default:return{}}}}},Mh=new Set(["left","top"]);async function Ww(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=nn(n),l=co(n),a=It(n)==="y",u=Mh.has(i)?-1:1,d=s&&a?-1:1,p=tn(t,e);let{mainAxis:h,crossAxis:f,alignmentAxis:b}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return l&&typeof b=="number"&&(f=l==="end"?b*-1:b),a?{x:f*d,y:h*u}:{x:h*u,y:f*d}}const Hw=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:l}=t,a=await Ww(t,e);return i===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:s+a.y,data:{...a,placement:i}}}}},Qw=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:l={fn:x=>{let{x:g,y:m}=x;return{x:g,y:m}}},...a}=tn(e,t),u={x:n,y:r},d=await ns(t,a),p=It(nn(o)),h=lc(p);let f=u[h],b=u[p];if(s){const x=h==="y"?"top":"left",g=h==="y"?"bottom":"right",m=f+d[x],v=f-d[g];f=Xa(m,f,v)}if(i){const x=p==="y"?"top":"left",g=p==="y"?"bottom":"right",m=b+d[x],v=b-d[g];b=Xa(m,b,v)}const y=l.fn({...t,[h]:f,[p]:b});return{...y,data:{x:y.x-n,y:y.y-r,enabled:{[h]:s,[p]:i}}}}}},Kw=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:l=0,mainAxis:a=!0,crossAxis:u=!0}=tn(e,t),d={x:n,y:r},p=It(o),h=lc(p);let f=d[h],b=d[p];const y=tn(l,t),x=typeof y=="number"?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(a){const v=h==="y"?"height":"width",S=s.reference[h]-s.floating[v]+x.mainAxis,E=s.reference[h]+s.reference[v]-x.mainAxis;f<S?f=S:f>E&&(f=E)}if(u){var g,m;const v=h==="y"?"width":"height",S=Mh.has(nn(o)),E=s.reference[p]-s.floating[v]+(S&&((g=i.offset)==null?void 0:g[p])||0)+(S?0:x.crossAxis),C=s.reference[p]+s.reference[v]+(S?0:((m=i.offset)==null?void 0:m[p])||0)-(S?x.crossAxis:0);b<E?b=E:b>C&&(b=C)}return{[h]:f,[p]:b}}}},Yw=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:l}=t,{apply:a=()=>{},...u}=tn(e,t),d=await ns(t,u),p=nn(o),h=co(o),f=It(o)==="y",{width:b,height:y}=s.floating;let x,g;p==="top"||p==="bottom"?(x=p,g=h===(await(i.isRTL==null?void 0:i.isRTL(l.floating))?"start":"end")?"left":"right"):(g=p,x=h==="end"?"top":"bottom");const m=y-d.top-d.bottom,v=b-d.left-d.right,S=$n(y-d[x],m),E=$n(b-d[g],v),C=!t.middlewareData.shift;let N=S,T=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(T=v),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(N=m),C&&!h){const M=qe(d.left,0),F=qe(d.right,0),D=qe(d.top,0),Q=qe(d.bottom,0);f?T=b-2*(M!==0||F!==0?M+F:qe(d.left,d.right)):N=y-2*(D!==0||Q!==0?D+Q:qe(d.top,d.bottom))}await a({...t,availableWidth:T,availableHeight:N});const O=await i.getDimensions(l.floating);return b!==O.width||y!==O.height?{reset:{rects:!0}}:{}}}};function ol(){return typeof window<"u"}function fo(e){return Oh(e)?(e.nodeName||"").toLowerCase():"#document"}function Je(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Bt(e){var t;return(t=(Oh(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Oh(e){return ol()?e instanceof Node||e instanceof Je(e).Node:!1}function jt(e){return ol()?e instanceof Element||e instanceof Je(e).Element:!1}function $t(e){return ol()?e instanceof HTMLElement||e instanceof Je(e).HTMLElement:!1}function Jd(e){return!ol()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Je(e).ShadowRoot}const Gw=new Set(["inline","contents"]);function hs(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Tt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Gw.has(o)}const qw=new Set(["table","td","th"]);function Xw(e){return qw.has(fo(e))}const Zw=[":popover-open",":modal"];function sl(e){return Zw.some(t=>{try{return e.matches(t)}catch{return!1}})}const Jw=["transform","translate","scale","rotate","perspective"],e1=["transform","translate","scale","rotate","perspective","filter"],t1=["paint","layout","strict","content"];function cc(e){const t=dc(),n=jt(e)?Tt(e):e;return Jw.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||e1.some(r=>(n.willChange||"").includes(r))||t1.some(r=>(n.contain||"").includes(r))}function n1(e){let t=Bn(e);for(;$t(t)&&!oo(t);){if(cc(t))return t;if(sl(t))return null;t=Bn(t)}return null}function dc(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const r1=new Set(["html","body","#document"]);function oo(e){return r1.has(fo(e))}function Tt(e){return Je(e).getComputedStyle(e)}function il(e){return jt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Bn(e){if(fo(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Jd(e)&&e.host||Bt(e);return Jd(t)?t.host:t}function _h(e){const t=Bn(e);return oo(t)?e.ownerDocument?e.ownerDocument.body:e.body:$t(t)&&hs(t)?t:_h(t)}function rs(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=_h(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=Je(o);if(s){const l=Ja(i);return t.concat(i,i.visualViewport||[],hs(o)?o:[],l&&n?rs(l):[])}return t.concat(o,rs(o,[],n))}function Ja(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Lh(e){const t=Tt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=$t(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,l=Ai(n)!==s||Ai(r)!==i;return l&&(n=s,r=i),{width:n,height:r,$:l}}function fc(e){return jt(e)?e:e.contextElement}function Br(e){const t=fc(e);if(!$t(t))return Ft(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=Lh(t);let i=(s?Ai(n.width):n.width)/r,l=(s?Ai(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!l||!Number.isFinite(l))&&(l=1),{x:i,y:l}}const o1=Ft(0);function Ih(e){const t=Je(e);return!dc()||!t.visualViewport?o1:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function s1(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Je(e)?!1:t}function fr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=fc(e);let i=Ft(1);t&&(r?jt(r)&&(i=Br(r)):i=Br(e));const l=s1(s,n,r)?Ih(s):Ft(0);let a=(o.left+l.x)/i.x,u=(o.top+l.y)/i.y,d=o.width/i.x,p=o.height/i.y;if(s){const h=Je(s),f=r&&jt(r)?Je(r):r;let b=h,y=Ja(b);for(;y&&r&&f!==b;){const x=Br(y),g=y.getBoundingClientRect(),m=Tt(y),v=g.left+(y.clientLeft+parseFloat(m.paddingLeft))*x.x,S=g.top+(y.clientTop+parseFloat(m.paddingTop))*x.y;a*=x.x,u*=x.y,d*=x.x,p*=x.y,a+=v,u+=S,b=Je(y),y=Ja(b)}}return Oi({width:d,height:p,x:a,y:u})}function pc(e,t){const n=il(e).scrollLeft;return t?t.left+n:fr(Bt(e)).left+n}function Dh(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:pc(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function i1(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Bt(r),l=t?sl(t.floating):!1;if(r===i||l&&s)return n;let a={scrollLeft:0,scrollTop:0},u=Ft(1);const d=Ft(0),p=$t(r);if((p||!p&&!s)&&((fo(r)!=="body"||hs(i))&&(a=il(r)),$t(r))){const f=fr(r);u=Br(r),d.x=f.x+r.clientLeft,d.y=f.y+r.clientTop}const h=i&&!p&&!s?Dh(i,a,!0):Ft(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+d.x+h.x,y:n.y*u.y-a.scrollTop*u.y+d.y+h.y}}function l1(e){return Array.from(e.getClientRects())}function a1(e){const t=Bt(e),n=il(e),r=e.ownerDocument.body,o=qe(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=qe(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+pc(e);const l=-n.scrollTop;return Tt(r).direction==="rtl"&&(i+=qe(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:l}}function u1(e,t){const n=Je(e),r=Bt(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,l=0,a=0;if(o){s=o.width,i=o.height;const u=dc();(!u||u&&t==="fixed")&&(l=o.offsetLeft,a=o.offsetTop)}return{width:s,height:i,x:l,y:a}}const c1=new Set(["absolute","fixed"]);function d1(e,t){const n=fr(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=$t(e)?Br(e):Ft(1),i=e.clientWidth*s.x,l=e.clientHeight*s.y,a=o*s.x,u=r*s.y;return{width:i,height:l,x:a,y:u}}function ef(e,t,n){let r;if(t==="viewport")r=u1(e,n);else if(t==="document")r=a1(Bt(e));else if(jt(t))r=d1(t,n);else{const o=Ih(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Oi(r)}function zh(e,t){const n=Bn(e);return n===t||!jt(n)||oo(n)?!1:Tt(n).position==="fixed"||zh(n,t)}function f1(e,t){const n=t.get(e);if(n)return n;let r=rs(e,[],!1).filter(l=>jt(l)&&fo(l)!=="body"),o=null;const s=Tt(e).position==="fixed";let i=s?Bn(e):e;for(;jt(i)&&!oo(i);){const l=Tt(i),a=cc(i);!a&&l.position==="fixed"&&(o=null),(s?!a&&!o:!a&&l.position==="static"&&!!o&&c1.has(o.position)||hs(i)&&!a&&zh(e,i))?r=r.filter(d=>d!==i):o=l,i=Bn(i)}return t.set(e,r),r}function p1(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?sl(t)?[]:f1(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((u,d)=>{const p=ef(t,d,o);return u.top=qe(p.top,u.top),u.right=$n(p.right,u.right),u.bottom=$n(p.bottom,u.bottom),u.left=qe(p.left,u.left),u},ef(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function m1(e){const{width:t,height:n}=Lh(e);return{width:t,height:n}}function h1(e,t,n){const r=$t(t),o=Bt(t),s=n==="fixed",i=fr(e,!0,s,t);let l={scrollLeft:0,scrollTop:0};const a=Ft(0);function u(){a.x=pc(o)}if(r||!r&&!s)if((fo(t)!=="body"||hs(o))&&(l=il(t)),r){const f=fr(t,!0,s,t);a.x=f.x+t.clientLeft,a.y=f.y+t.clientTop}else o&&u();s&&!r&&o&&u();const d=o&&!r&&!s?Dh(o,l):Ft(0),p=i.left+l.scrollLeft-a.x-d.x,h=i.top+l.scrollTop-a.y-d.y;return{x:p,y:h,width:i.width,height:i.height}}function Gl(e){return Tt(e).position==="static"}function tf(e,t){if(!$t(e)||Tt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Bt(e)===n&&(n=n.ownerDocument.body),n}function Fh(e,t){const n=Je(e);if(sl(e))return n;if(!$t(e)){let o=Bn(e);for(;o&&!oo(o);){if(jt(o)&&!Gl(o))return o;o=Bn(o)}return n}let r=tf(e,t);for(;r&&Xw(r)&&Gl(r);)r=tf(r,t);return r&&oo(r)&&Gl(r)&&!cc(r)?n:r||n1(e)||n}const g1=async function(e){const t=this.getOffsetParent||Fh,n=this.getDimensions,r=await n(e.floating);return{reference:h1(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function v1(e){return Tt(e).direction==="rtl"}const y1={convertOffsetParentRelativeRectToViewportRelativeRect:i1,getDocumentElement:Bt,getClippingRect:p1,getOffsetParent:Fh,getElementRects:g1,getClientRects:l1,getDimensions:m1,getScale:Br,isElement:jt,isRTL:v1};function $h(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function x1(e,t){let n=null,r;const o=Bt(e);function s(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function i(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),s();const u=e.getBoundingClientRect(),{left:d,top:p,width:h,height:f}=u;if(l||t(),!h||!f)return;const b=$s(p),y=$s(o.clientWidth-(d+h)),x=$s(o.clientHeight-(p+f)),g=$s(d),v={rootMargin:-b+"px "+-y+"px "+-x+"px "+-g+"px",threshold:qe(0,$n(1,a))||1};let S=!0;function E(C){const N=C[0].intersectionRatio;if(N!==a){if(!S)return i();N?i(!1,N):r=setTimeout(()=>{i(!1,1e-7)},1e3)}N===1&&!$h(u,e.getBoundingClientRect())&&i(),S=!1}try{n=new IntersectionObserver(E,{...v,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,v)}n.observe(e)}return i(!0),s}function w1(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,u=fc(e),d=o||s?[...u?rs(u):[],...rs(t)]:[];d.forEach(g=>{o&&g.addEventListener("scroll",n,{passive:!0}),s&&g.addEventListener("resize",n)});const p=u&&l?x1(u,n):null;let h=-1,f=null;i&&(f=new ResizeObserver(g=>{let[m]=g;m&&m.target===u&&f&&(f.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var v;(v=f)==null||v.observe(t)})),n()}),u&&!a&&f.observe(u),f.observe(t));let b,y=a?fr(e):null;a&&x();function x(){const g=fr(e);y&&!$h(y,g)&&n(),y=g,b=requestAnimationFrame(x)}return n(),()=>{var g;d.forEach(m=>{o&&m.removeEventListener("scroll",n),s&&m.removeEventListener("resize",n)}),p==null||p(),(g=f)==null||g.disconnect(),f=null,a&&cancelAnimationFrame(b)}}const b1=Hw,S1=Qw,E1=Uw,C1=Yw,N1=Vw,nf=Bw,k1=Kw,P1=(e,t,n)=>{const r=new Map,o={platform:y1,...n},s={...o.platform,_c:r};return $w(e,t,{...o,platform:s})};var j1=typeof document<"u",T1=function(){},ni=j1?w.useLayoutEffect:T1;function _i(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!_i(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!_i(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Bh(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function rf(e,t){const n=Bh(e);return Math.round(t*n)/n}function ql(e){const t=w.useRef(e);return ni(()=>{t.current=e}),t}function R1(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:l=!0,whileElementsMounted:a,open:u}=e,[d,p]=w.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,f]=w.useState(r);_i(h,r)||f(r);const[b,y]=w.useState(null),[x,g]=w.useState(null),m=w.useCallback(P=>{P!==C.current&&(C.current=P,y(P))},[]),v=w.useCallback(P=>{P!==N.current&&(N.current=P,g(P))},[]),S=s||b,E=i||x,C=w.useRef(null),N=w.useRef(null),T=w.useRef(d),O=a!=null,M=ql(a),F=ql(o),D=ql(u),Q=w.useCallback(()=>{if(!C.current||!N.current)return;const P={placement:t,strategy:n,middleware:h};F.current&&(P.platform=F.current),P1(C.current,N.current,P).then(j=>{const L={...j,isPositioned:D.current!==!1};_.current&&!_i(T.current,L)&&(T.current=L,ps.flushSync(()=>{p(L)}))})},[h,t,n,F,D]);ni(()=>{u===!1&&T.current.isPositioned&&(T.current.isPositioned=!1,p(P=>({...P,isPositioned:!1})))},[u]);const _=w.useRef(!1);ni(()=>(_.current=!0,()=>{_.current=!1}),[]),ni(()=>{if(S&&(C.current=S),E&&(N.current=E),S&&E){if(M.current)return M.current(S,E,Q);Q()}},[S,E,Q,M,O]);const G=w.useMemo(()=>({reference:C,floating:N,setReference:m,setFloating:v}),[m,v]),$=w.useMemo(()=>({reference:S,floating:E}),[S,E]),V=w.useMemo(()=>{const P={position:n,left:0,top:0};if(!$.floating)return P;const j=rf($.floating,d.x),L=rf($.floating,d.y);return l?{...P,transform:"translate("+j+"px, "+L+"px)",...Bh($.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:j,top:L}},[n,l,$.floating,d.x,d.y]);return w.useMemo(()=>({...d,update:Q,refs:G,elements:$,floatingStyles:V}),[d,Q,G,$,V])}const A1=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?nf({element:r.current,padding:o}).fn(n):{}:r?nf({element:r,padding:o}).fn(n):{}}}},M1=(e,t)=>({...b1(e),options:[e,t]}),O1=(e,t)=>({...S1(e),options:[e,t]}),_1=(e,t)=>({...k1(e),options:[e,t]}),L1=(e,t)=>({...E1(e),options:[e,t]}),I1=(e,t)=>({...C1(e),options:[e,t]}),D1=(e,t)=>({...N1(e),options:[e,t]}),z1=(e,t)=>({...A1(e),options:[e,t]});var F1="Arrow",Uh=w.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return c.jsx(Ye.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:c.jsx("polygon",{points:"0,0 30,0 15,10"})})});Uh.displayName=F1;var $1=Uh;function B1(e){const[t,n]=w.useState(void 0);return Fn(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,l;if("borderBoxSize"in s){const a=s.borderBoxSize,u=Array.isArray(a)?a[0]:a;i=u.inlineSize,l=u.blockSize}else i=e.offsetWidth,l=e.offsetHeight;n({width:i,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var Vh="Popper",[Wh,Hh]=tl(Vh),[$b,Qh]=Wh(Vh),Kh="PopperAnchor",Yh=w.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=Qh(Kh,n),i=w.useRef(null),l=Pt(t,i);return w.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:c.jsx(Ye.div,{...o,ref:l})});Yh.displayName=Kh;var mc="PopperContent",[U1,V1]=Wh(mc),Gh=w.forwardRef((e,t)=>{var J,hr,on,Hn,sn,gr;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:l=0,avoidCollisions:a=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:p="partial",hideWhenDetached:h=!1,updatePositionStrategy:f="optimized",onPlaced:b,...y}=e,x=Qh(mc,n),[g,m]=w.useState(null),v=Pt(t,ln=>m(ln)),[S,E]=w.useState(null),C=B1(S),N=(C==null?void 0:C.width)??0,T=(C==null?void 0:C.height)??0,O=r+(s!=="center"?"-"+s:""),M=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},F=Array.isArray(u)?u:[u],D=F.length>0,Q={padding:M,boundary:F.filter(H1),altBoundary:D},{refs:_,floatingStyles:G,placement:$,isPositioned:V,middlewareData:P}=R1({strategy:"fixed",placement:O,whileElementsMounted:(...ln)=>w1(...ln,{animationFrame:f==="always"}),elements:{reference:x.anchor},middleware:[M1({mainAxis:o+T,alignmentAxis:i}),a&&O1({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?_1():void 0,...Q}),a&&L1({...Q}),I1({...Q,apply:({elements:ln,rects:gs,availableWidth:ml,availableHeight:vs})=>{const{width:hl,height:po}=gs.reference,vr=ln.floating.style;vr.setProperty("--radix-popper-available-width",`${ml}px`),vr.setProperty("--radix-popper-available-height",`${vs}px`),vr.setProperty("--radix-popper-anchor-width",`${hl}px`),vr.setProperty("--radix-popper-anchor-height",`${po}px`)}}),S&&z1({element:S,padding:l}),Q1({arrowWidth:N,arrowHeight:T}),h&&D1({strategy:"referenceHidden",...Q})]}),[j,L]=Zh($),W=zn(b);Fn(()=>{V&&(W==null||W())},[V,W]);const z=(J=P.arrow)==null?void 0:J.x,K=(hr=P.arrow)==null?void 0:hr.y,q=((on=P.arrow)==null?void 0:on.centerOffset)!==0,[me,Ne]=w.useState();return Fn(()=>{g&&Ne(window.getComputedStyle(g).zIndex)},[g]),c.jsx("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:V?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:me,"--radix-popper-transform-origin":[(Hn=P.transformOrigin)==null?void 0:Hn.x,(sn=P.transformOrigin)==null?void 0:sn.y].join(" "),...((gr=P.hide)==null?void 0:gr.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:c.jsx(U1,{scope:n,placedSide:j,onArrowChange:E,arrowX:z,arrowY:K,shouldHideArrow:q,children:c.jsx(Ye.div,{"data-side":j,"data-align":L,...y,ref:v,style:{...y.style,animation:V?void 0:"none"}})})})});Gh.displayName=mc;var qh="PopperArrow",W1={top:"bottom",right:"left",bottom:"top",left:"right"},Xh=w.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=V1(qh,r),i=W1[s.placedSide];return c.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:c.jsx($1,{...o,ref:n,style:{...o.style,display:"block"}})})});Xh.displayName=qh;function H1(e){return e!==null}var Q1=e=>({name:"transformOrigin",options:e,fn(t){var x,g,m;const{placement:n,rects:r,middlewareData:o}=t,i=((x=o.arrow)==null?void 0:x.centerOffset)!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,d]=Zh(n),p={start:"0%",center:"50%",end:"100%"}[d],h=(((g=o.arrow)==null?void 0:g.x)??0)+l/2,f=(((m=o.arrow)==null?void 0:m.y)??0)+a/2;let b="",y="";return u==="bottom"?(b=i?p:`${h}px`,y=`${-a}px`):u==="top"?(b=i?p:`${h}px`,y=`${r.floating.height+a}px`):u==="right"?(b=`${-a}px`,y=i?p:`${f}px`):u==="left"&&(b=`${r.floating.width+a}px`,y=i?p:`${f}px`),{data:{x:b,y}}}});function Zh(e){const[t,n="center"]=e.split("-");return[t,n]}var K1=Yh,Y1=Gh,G1=Xh,[ll,Bb]=tl("Tooltip",[Hh]),hc=Hh(),Jh="TooltipProvider",q1=700,of="tooltip.open",[X1,eg]=ll(Jh),tg=e=>{const{__scopeTooltip:t,delayDuration:n=q1,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,i=w.useRef(!0),l=w.useRef(!1),a=w.useRef(0);return w.useEffect(()=>{const u=a.current;return()=>window.clearTimeout(u)},[]),c.jsx(X1,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:w.useCallback(()=>{window.clearTimeout(a.current),i.current=!1},[]),onClose:w.useCallback(()=>{window.clearTimeout(a.current),a.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:w.useCallback(u=>{l.current=u},[]),disableHoverableContent:o,children:s})};tg.displayName=Jh;var ng="Tooltip",[Ub,al]=ll(ng),eu="TooltipTrigger",Z1=w.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=al(eu,n),s=eg(eu,n),i=hc(n),l=w.useRef(null),a=Pt(t,l,o.onTriggerChange),u=w.useRef(!1),d=w.useRef(!1),p=w.useCallback(()=>u.current=!1,[]);return w.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),c.jsx(K1,{asChild:!0,...i,children:c.jsx(Ye.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:a,onPointerMove:ye(e.onPointerMove,h=>{h.pointerType!=="touch"&&!d.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:ye(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:ye(e.onPointerDown,()=>{o.open&&o.onClose(),u.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:ye(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:ye(e.onBlur,o.onClose),onClick:ye(e.onClick,o.onClose)})})});Z1.displayName=eu;var J1="TooltipPortal",[Vb,e2]=ll(J1,{forceMount:void 0}),so="TooltipContent",rg=w.forwardRef((e,t)=>{const n=e2(so,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=al(so,e.__scopeTooltip);return c.jsx(tc,{present:r||i.open,children:i.disableHoverableContent?c.jsx(og,{side:o,...s,ref:t}):c.jsx(t2,{side:o,...s,ref:t})})}),t2=w.forwardRef((e,t)=>{const n=al(so,e.__scopeTooltip),r=eg(so,e.__scopeTooltip),o=w.useRef(null),s=Pt(t,o),[i,l]=w.useState(null),{trigger:a,onClose:u}=n,d=o.current,{onPointerInTransitChange:p}=r,h=w.useCallback(()=>{l(null),p(!1)},[p]),f=w.useCallback((b,y)=>{const x=b.currentTarget,g={x:b.clientX,y:b.clientY},m=i2(g,x.getBoundingClientRect()),v=l2(g,m),S=a2(y.getBoundingClientRect()),E=c2([...v,...S]);l(E),p(!0)},[p]);return w.useEffect(()=>()=>h(),[h]),w.useEffect(()=>{if(a&&d){const b=x=>f(x,d),y=x=>f(x,a);return a.addEventListener("pointerleave",b),d.addEventListener("pointerleave",y),()=>{a.removeEventListener("pointerleave",b),d.removeEventListener("pointerleave",y)}}},[a,d,f,h]),w.useEffect(()=>{if(i){const b=y=>{const x=y.target,g={x:y.clientX,y:y.clientY},m=(a==null?void 0:a.contains(x))||(d==null?void 0:d.contains(x)),v=!u2(g,i);m?h():v&&(h(),u())};return document.addEventListener("pointermove",b),()=>document.removeEventListener("pointermove",b)}},[a,d,i,u,h]),c.jsx(og,{...e,ref:s})}),[n2,r2]=ll(ng,{isInside:!1}),o2=m0("TooltipContent"),og=w.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...l}=e,a=al(so,n),u=hc(n),{onClose:d}=a;return w.useEffect(()=>(document.addEventListener(of,d),()=>document.removeEventListener(of,d)),[d]),w.useEffect(()=>{if(a.trigger){const p=h=>{const f=h.target;f!=null&&f.contains(a.trigger)&&d()};return window.addEventListener("scroll",p,{capture:!0}),()=>window.removeEventListener("scroll",p,{capture:!0})}},[a.trigger,d]),c.jsx(ec,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:p=>p.preventDefault(),onDismiss:d,children:c.jsxs(Y1,{"data-state":a.stateAttribute,...u,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[c.jsx(o2,{children:r}),c.jsx(n2,{scope:n,isInside:!0,children:c.jsx(F0,{id:a.contentId,role:"tooltip",children:o||r})})]})})});rg.displayName=so;var sg="TooltipArrow",s2=w.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=hc(n);return r2(sg,n).isInside?null:c.jsx(G1,{...o,...r,ref:t})});s2.displayName=sg;function i2(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function l2(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function a2(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function u2(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const l=t[s],a=t[i],u=l.x,d=l.y,p=a.x,h=a.y;d>r!=h>r&&n<(p-u)*(r-d)/(h-d)+u&&(o=!o)}return o}function c2(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),d2(t)}function d2(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var f2=tg,ig=rg;const p2=f2,m2=w.forwardRef(({className:e,sideOffset:t=4,...n},r)=>c.jsx(ig,{ref:r,sideOffset:t,className:_e("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));m2.displayName=ig.displayName;var ul=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},cl=typeof window>"u"||"Deno"in globalThis;function xt(){}function h2(e,t){return typeof e=="function"?e(t):e}function g2(e){return typeof e=="number"&&e>=0&&e!==1/0}function v2(e,t){return Math.max(e+(t||0)-Date.now(),0)}function tu(e,t){return typeof e=="function"?e(t):e}function y2(e,t){return typeof e=="function"?e(t):e}function sf(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:s,queryKey:i,stale:l}=e;if(i){if(r){if(t.queryHash!==gc(i,t.options))return!1}else if(!ss(t.queryKey,i))return!1}if(n!=="all"){const a=t.isActive();if(n==="active"&&!a||n==="inactive"&&a)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||o&&o!==t.state.fetchStatus||s&&!s(t))}function lf(e,t){const{exact:n,status:r,predicate:o,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(n){if(os(t.options.mutationKey)!==os(s))return!1}else if(!ss(t.options.mutationKey,s))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function gc(e,t){return((t==null?void 0:t.queryKeyHashFn)||os)(e)}function os(e){return JSON.stringify(e,(t,n)=>nu(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function ss(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(n=>ss(e[n],t[n])):!1}function lg(e,t){if(e===t)return e;const n=af(e)&&af(t);if(n||nu(e)&&nu(t)){const r=n?e:Object.keys(e),o=r.length,s=n?t:Object.keys(t),i=s.length,l=n?[]:{},a=new Set(r);let u=0;for(let d=0;d<i;d++){const p=n?d:s[d];(!n&&a.has(p)||n)&&e[p]===void 0&&t[p]===void 0?(l[p]=void 0,u++):(l[p]=lg(e[p],t[p]),l[p]===e[p]&&e[p]!==void 0&&u++)}return o===i&&u===o?e:l}return t}function af(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function nu(e){if(!uf(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!uf(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function uf(e){return Object.prototype.toString.call(e)==="[object Object]"}function x2(e){return new Promise(t=>{setTimeout(t,e)})}function w2(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?lg(e,t):t}function b2(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function S2(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var vc=Symbol();function ag(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===vc?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var Jn,xn,Vr,wf,E2=(wf=class extends ul{constructor(){super();Z(this,Jn);Z(this,xn);Z(this,Vr);U(this,Vr,t=>{if(!cl&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){k(this,xn)||this.setEventListener(k(this,Vr))}onUnsubscribe(){var t;this.hasListeners()||((t=k(this,xn))==null||t.call(this),U(this,xn,void 0))}setEventListener(t){var n;U(this,Vr,t),(n=k(this,xn))==null||n.call(this),U(this,xn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){k(this,Jn)!==t&&(U(this,Jn,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof k(this,Jn)=="boolean"?k(this,Jn):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Jn=new WeakMap,xn=new WeakMap,Vr=new WeakMap,wf),ug=new E2,Wr,wn,Hr,bf,C2=(bf=class extends ul{constructor(){super();Z(this,Wr,!0);Z(this,wn);Z(this,Hr);U(this,Hr,t=>{if(!cl&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){k(this,wn)||this.setEventListener(k(this,Hr))}onUnsubscribe(){var t;this.hasListeners()||((t=k(this,wn))==null||t.call(this),U(this,wn,void 0))}setEventListener(t){var n;U(this,Hr,t),(n=k(this,wn))==null||n.call(this),U(this,wn,t(this.setOnline.bind(this)))}setOnline(t){k(this,Wr)!==t&&(U(this,Wr,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return k(this,Wr)}},Wr=new WeakMap,wn=new WeakMap,Hr=new WeakMap,bf),Li=new C2;function N2(){let e,t;const n=new Promise((o,s)=>{e=o,t=s});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function k2(e){return Math.min(1e3*2**e,3e4)}function cg(e){return(e??"online")==="online"?Li.isOnline():!0}var dg=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Xl(e){return e instanceof dg}function fg(e){let t=!1,n=0,r=!1,o;const s=N2(),i=y=>{var x;r||(h(new dg(y)),(x=e.abort)==null||x.call(e))},l=()=>{t=!0},a=()=>{t=!1},u=()=>ug.isFocused()&&(e.networkMode==="always"||Li.isOnline())&&e.canRun(),d=()=>cg(e.networkMode)&&e.canRun(),p=y=>{var x;r||(r=!0,(x=e.onSuccess)==null||x.call(e,y),o==null||o(),s.resolve(y))},h=y=>{var x;r||(r=!0,(x=e.onError)==null||x.call(e,y),o==null||o(),s.reject(y))},f=()=>new Promise(y=>{var x;o=g=>{(r||u())&&y(g)},(x=e.onPause)==null||x.call(e)}).then(()=>{var y;o=void 0,r||(y=e.onContinue)==null||y.call(e)}),b=()=>{if(r)return;let y;const x=n===0?e.initialPromise:void 0;try{y=x??e.fn()}catch(g){y=Promise.reject(g)}Promise.resolve(y).then(p).catch(g=>{var C;if(r)return;const m=e.retry??(cl?0:3),v=e.retryDelay??k2,S=typeof v=="function"?v(n,g):v,E=m===!0||typeof m=="number"&&n<m||typeof m=="function"&&m(n,g);if(t||!E){h(g);return}n++,(C=e.onFail)==null||C.call(e,n,g),x2(S).then(()=>u()?void 0:f()).then(()=>{t?h(g):b()})})};return{promise:s,cancel:i,continue:()=>(o==null||o(),s),cancelRetry:l,continueRetry:a,canStart:d,start:()=>(d()?b():f().then(b),s)}}var P2=e=>setTimeout(e,0);function j2(){let e=[],t=0,n=l=>{l()},r=l=>{l()},o=P2;const s=l=>{t?e.push(l):o(()=>{n(l)})},i=()=>{const l=e;e=[],l.length&&o(()=>{r(()=>{l.forEach(a=>{n(a)})})})};return{batch:l=>{let a;t++;try{a=l()}finally{t--,t||i()}return a},batchCalls:l=>(...a)=>{s(()=>{l(...a)})},schedule:s,setNotifyFunction:l=>{n=l},setBatchNotifyFunction:l=>{r=l},setScheduler:l=>{o=l}}}var De=j2(),er,Sf,pg=(Sf=class{constructor(){Z(this,er)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),g2(this.gcTime)&&U(this,er,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(cl?1/0:5*60*1e3))}clearGcTimeout(){k(this,er)&&(clearTimeout(k(this,er)),U(this,er,void 0))}},er=new WeakMap,Sf),Qr,tr,ot,nr,Te,is,rr,wt,Vt,Ef,T2=(Ef=class extends pg{constructor(t){super();Z(this,wt);Z(this,Qr);Z(this,tr);Z(this,ot);Z(this,nr);Z(this,Te);Z(this,is);Z(this,rr);U(this,rr,!1),U(this,is,t.defaultOptions),this.setOptions(t.options),this.observers=[],U(this,nr,t.client),U(this,ot,k(this,nr).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,U(this,Qr,A2(this.options)),this.state=t.state??k(this,Qr),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=k(this,Te))==null?void 0:t.promise}setOptions(t){this.options={...k(this,is),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&k(this,ot).remove(this)}setData(t,n){const r=w2(this.state.data,t,this.options);return ke(this,wt,Vt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){ke(this,wt,Vt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=k(this,Te))==null?void 0:r.promise;return(o=k(this,Te))==null||o.cancel(t),n?n.then(xt).catch(xt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(k(this,Qr))}isActive(){return this.observers.some(t=>y2(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===vc||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>tu(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!v2(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=k(this,Te))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=k(this,Te))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),k(this,ot).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(k(this,Te)&&(k(this,rr)?k(this,Te).cancel({revert:!0}):k(this,Te).cancelRetry()),this.scheduleGc()),k(this,ot).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||ke(this,wt,Vt).call(this,{type:"invalidate"})}fetch(t,n){var u,d,p;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(k(this,Te))return k(this,Te).continueRetry(),k(this,Te).promise}if(t&&this.setOptions(t),!this.options.queryFn){const h=this.observers.find(f=>f.options.queryFn);h&&this.setOptions(h.options)}const r=new AbortController,o=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(U(this,rr,!0),r.signal)})},s=()=>{const h=ag(this.options,n),b=(()=>{const y={client:k(this,nr),queryKey:this.queryKey,meta:this.meta};return o(y),y})();return U(this,rr,!1),this.options.persister?this.options.persister(h,b,this):h(b)},l=(()=>{const h={fetchOptions:n,options:this.options,queryKey:this.queryKey,client:k(this,nr),state:this.state,fetchFn:s};return o(h),h})();(u=this.options.behavior)==null||u.onFetch(l,this),U(this,tr,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((d=l.fetchOptions)==null?void 0:d.meta))&&ke(this,wt,Vt).call(this,{type:"fetch",meta:(p=l.fetchOptions)==null?void 0:p.meta});const a=h=>{var f,b,y,x;Xl(h)&&h.silent||ke(this,wt,Vt).call(this,{type:"error",error:h}),Xl(h)||((b=(f=k(this,ot).config).onError)==null||b.call(f,h,this),(x=(y=k(this,ot).config).onSettled)==null||x.call(y,this.state.data,h,this)),this.scheduleGc()};return U(this,Te,fg({initialPromise:n==null?void 0:n.initialPromise,fn:l.fetchFn,abort:r.abort.bind(r),onSuccess:h=>{var f,b,y,x;if(h===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(h)}catch(g){a(g);return}(b=(f=k(this,ot).config).onSuccess)==null||b.call(f,h,this),(x=(y=k(this,ot).config).onSettled)==null||x.call(y,h,this.state.error,this),this.scheduleGc()},onError:a,onFail:(h,f)=>{ke(this,wt,Vt).call(this,{type:"failed",failureCount:h,error:f})},onPause:()=>{ke(this,wt,Vt).call(this,{type:"pause"})},onContinue:()=>{ke(this,wt,Vt).call(this,{type:"continue"})},retry:l.options.retry,retryDelay:l.options.retryDelay,networkMode:l.options.networkMode,canRun:()=>!0})),k(this,Te).start()}},Qr=new WeakMap,tr=new WeakMap,ot=new WeakMap,nr=new WeakMap,Te=new WeakMap,is=new WeakMap,rr=new WeakMap,wt=new WeakSet,Vt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...R2(r.data,this.options),fetchMeta:t.meta??null};case"success":return U(this,tr,void 0),{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return Xl(o)&&o.revert&&k(this,tr)?{...k(this,tr),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),De.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),k(this,ot).notify({query:this,type:"updated",action:t})})},Ef);function R2(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:cg(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function A2(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Mt,Cf,M2=(Cf=class extends ul{constructor(t={}){super();Z(this,Mt);this.config=t,U(this,Mt,new Map)}build(t,n,r){const o=n.queryKey,s=n.queryHash??gc(o,n);let i=this.get(s);return i||(i=new T2({client:t,queryKey:o,queryHash:s,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(i)),i}add(t){k(this,Mt).has(t.queryHash)||(k(this,Mt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=k(this,Mt).get(t.queryHash);n&&(t.destroy(),n===t&&k(this,Mt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){De.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return k(this,Mt).get(t)}getAll(){return[...k(this,Mt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>sf(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>sf(t,r)):n}notify(t){De.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){De.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){De.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Mt=new WeakMap,Cf),Ot,Le,or,_t,mn,Nf,O2=(Nf=class extends pg{constructor(t){super();Z(this,_t);Z(this,Ot);Z(this,Le);Z(this,or);this.mutationId=t.mutationId,U(this,Le,t.mutationCache),U(this,Ot,[]),this.state=t.state||_2(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){k(this,Ot).includes(t)||(k(this,Ot).push(t),this.clearGcTimeout(),k(this,Le).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){U(this,Ot,k(this,Ot).filter(n=>n!==t)),this.scheduleGc(),k(this,Le).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){k(this,Ot).length||(this.state.status==="pending"?this.scheduleGc():k(this,Le).remove(this))}continue(){var t;return((t=k(this,or))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var s,i,l,a,u,d,p,h,f,b,y,x,g,m,v,S,E,C,N,T;const n=()=>{ke(this,_t,mn).call(this,{type:"continue"})};U(this,or,fg({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(O,M)=>{ke(this,_t,mn).call(this,{type:"failed",failureCount:O,error:M})},onPause:()=>{ke(this,_t,mn).call(this,{type:"pause"})},onContinue:n,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>k(this,Le).canRun(this)}));const r=this.state.status==="pending",o=!k(this,or).canStart();try{if(r)n();else{ke(this,_t,mn).call(this,{type:"pending",variables:t,isPaused:o}),await((i=(s=k(this,Le).config).onMutate)==null?void 0:i.call(s,t,this));const M=await((a=(l=this.options).onMutate)==null?void 0:a.call(l,t));M!==this.state.context&&ke(this,_t,mn).call(this,{type:"pending",context:M,variables:t,isPaused:o})}const O=await k(this,or).start();return await((d=(u=k(this,Le).config).onSuccess)==null?void 0:d.call(u,O,t,this.state.context,this)),await((h=(p=this.options).onSuccess)==null?void 0:h.call(p,O,t,this.state.context)),await((b=(f=k(this,Le).config).onSettled)==null?void 0:b.call(f,O,null,this.state.variables,this.state.context,this)),await((x=(y=this.options).onSettled)==null?void 0:x.call(y,O,null,t,this.state.context)),ke(this,_t,mn).call(this,{type:"success",data:O}),O}catch(O){try{throw await((m=(g=k(this,Le).config).onError)==null?void 0:m.call(g,O,t,this.state.context,this)),await((S=(v=this.options).onError)==null?void 0:S.call(v,O,t,this.state.context)),await((C=(E=k(this,Le).config).onSettled)==null?void 0:C.call(E,void 0,O,this.state.variables,this.state.context,this)),await((T=(N=this.options).onSettled)==null?void 0:T.call(N,void 0,O,t,this.state.context)),O}finally{ke(this,_t,mn).call(this,{type:"error",error:O})}}finally{k(this,Le).runNext(this)}}},Ot=new WeakMap,Le=new WeakMap,or=new WeakMap,_t=new WeakSet,mn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),De.batch(()=>{k(this,Ot).forEach(r=>{r.onMutationUpdate(t)}),k(this,Le).notify({mutation:this,type:"updated",action:t})})},Nf);function _2(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Qt,bt,ls,kf,L2=(kf=class extends ul{constructor(t={}){super();Z(this,Qt);Z(this,bt);Z(this,ls);this.config=t,U(this,Qt,new Set),U(this,bt,new Map),U(this,ls,0)}build(t,n,r){const o=new O2({mutationCache:this,mutationId:++xs(this,ls)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){k(this,Qt).add(t);const n=Bs(t);if(typeof n=="string"){const r=k(this,bt).get(n);r?r.push(t):k(this,bt).set(n,[t])}this.notify({type:"added",mutation:t})}remove(t){if(k(this,Qt).delete(t)){const n=Bs(t);if(typeof n=="string"){const r=k(this,bt).get(n);if(r)if(r.length>1){const o=r.indexOf(t);o!==-1&&r.splice(o,1)}else r[0]===t&&k(this,bt).delete(n)}}this.notify({type:"removed",mutation:t})}canRun(t){const n=Bs(t);if(typeof n=="string"){const r=k(this,bt).get(n),o=r==null?void 0:r.find(s=>s.state.status==="pending");return!o||o===t}else return!0}runNext(t){var r;const n=Bs(t);if(typeof n=="string"){const o=(r=k(this,bt).get(n))==null?void 0:r.find(s=>s!==t&&s.state.isPaused);return(o==null?void 0:o.continue())??Promise.resolve()}else return Promise.resolve()}clear(){De.batch(()=>{k(this,Qt).forEach(t=>{this.notify({type:"removed",mutation:t})}),k(this,Qt).clear(),k(this,bt).clear()})}getAll(){return Array.from(k(this,Qt))}find(t){const n={exact:!0,...t};return this.getAll().find(r=>lf(n,r))}findAll(t={}){return this.getAll().filter(n=>lf(t,n))}notify(t){De.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return De.batch(()=>Promise.all(t.map(n=>n.continue().catch(xt))))}},Qt=new WeakMap,bt=new WeakMap,ls=new WeakMap,kf);function Bs(e){var t;return(t=e.options.scope)==null?void 0:t.id}function cf(e){return{onFetch:(t,n)=>{var d,p,h,f,b;const r=t.options,o=(h=(p=(d=t.fetchOptions)==null?void 0:d.meta)==null?void 0:p.fetchMore)==null?void 0:h.direction,s=((f=t.state.data)==null?void 0:f.pages)||[],i=((b=t.state.data)==null?void 0:b.pageParams)||[];let l={pages:[],pageParams:[]},a=0;const u=async()=>{let y=!1;const x=v=>{Object.defineProperty(v,"signal",{enumerable:!0,get:()=>(t.signal.aborted?y=!0:t.signal.addEventListener("abort",()=>{y=!0}),t.signal)})},g=ag(t.options,t.fetchOptions),m=async(v,S,E)=>{if(y)return Promise.reject();if(S==null&&v.pages.length)return Promise.resolve(v);const N=(()=>{const F={client:t.client,queryKey:t.queryKey,pageParam:S,direction:E?"backward":"forward",meta:t.options.meta};return x(F),F})(),T=await g(N),{maxPages:O}=t.options,M=E?S2:b2;return{pages:M(v.pages,T,O),pageParams:M(v.pageParams,S,O)}};if(o&&s.length){const v=o==="backward",S=v?I2:df,E={pages:s,pageParams:i},C=S(r,E);l=await m(E,C,v)}else{const v=e??s.length;do{const S=a===0?i[0]??r.initialPageParam:df(r,l);if(a>0&&S==null)break;l=await m(l,S),a++}while(a<v)}return l};t.options.persister?t.fetchFn=()=>{var y,x;return(x=(y=t.options).persister)==null?void 0:x.call(y,u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function df(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function I2(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var de,bn,Sn,Kr,Yr,En,Gr,qr,Pf,D2=(Pf=class{constructor(e={}){Z(this,de);Z(this,bn);Z(this,Sn);Z(this,Kr);Z(this,Yr);Z(this,En);Z(this,Gr);Z(this,qr);U(this,de,e.queryCache||new M2),U(this,bn,e.mutationCache||new L2),U(this,Sn,e.defaultOptions||{}),U(this,Kr,new Map),U(this,Yr,new Map),U(this,En,0)}mount(){xs(this,En)._++,k(this,En)===1&&(U(this,Gr,ug.subscribe(async e=>{e&&(await this.resumePausedMutations(),k(this,de).onFocus())})),U(this,qr,Li.subscribe(async e=>{e&&(await this.resumePausedMutations(),k(this,de).onOnline())})))}unmount(){var e,t;xs(this,En)._--,k(this,En)===0&&((e=k(this,Gr))==null||e.call(this),U(this,Gr,void 0),(t=k(this,qr))==null||t.call(this),U(this,qr,void 0))}isFetching(e){return k(this,de).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return k(this,bn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=k(this,de).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),n=k(this,de).build(this,t),r=n.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&n.isStaleByTime(tu(t.staleTime,n))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return k(this,de).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=k(this,de).get(r.queryHash),s=o==null?void 0:o.state.data,i=h2(t,s);if(i!==void 0)return k(this,de).build(this,r).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return De.batch(()=>k(this,de).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=k(this,de).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=k(this,de);De.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=k(this,de);return De.batch(()=>(n.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const n={revert:!0,...t},r=De.batch(()=>k(this,de).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(xt).catch(xt)}invalidateQueries(e,t={}){return De.batch(()=>(k(this,de).findAll(e).forEach(n=>{n.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const n={...t,cancelRefetch:t.cancelRefetch??!0},r=De.batch(()=>k(this,de).findAll(e).filter(o=>!o.isDisabled()&&!o.isStatic()).map(o=>{let s=o.fetch(void 0,n);return n.throwOnError||(s=s.catch(xt)),o.state.fetchStatus==="paused"?Promise.resolve():s}));return Promise.all(r).then(xt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=k(this,de).build(this,t);return n.isStaleByTime(tu(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(xt).catch(xt)}fetchInfiniteQuery(e){return e.behavior=cf(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(xt).catch(xt)}ensureInfiniteQueryData(e){return e.behavior=cf(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Li.isOnline()?k(this,bn).resumePausedMutations():Promise.resolve()}getQueryCache(){return k(this,de)}getMutationCache(){return k(this,bn)}getDefaultOptions(){return k(this,Sn)}setDefaultOptions(e){U(this,Sn,e)}setQueryDefaults(e,t){k(this,Kr).set(os(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...k(this,Kr).values()],n={};return t.forEach(r=>{ss(e,r.queryKey)&&Object.assign(n,r.defaultOptions)}),n}setMutationDefaults(e,t){k(this,Yr).set(os(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...k(this,Yr).values()],n={};return t.forEach(r=>{ss(e,r.mutationKey)&&Object.assign(n,r.defaultOptions)}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...k(this,Sn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=gc(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===vc&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...k(this,Sn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){k(this,de).clear(),k(this,bn).clear()}},de=new WeakMap,bn=new WeakMap,Sn=new WeakMap,Kr=new WeakMap,Yr=new WeakMap,En=new WeakMap,Gr=new WeakMap,qr=new WeakMap,Pf),z2=w.createContext(void 0),F2=({client:e,children:t})=>(w.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),c.jsx(z2.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ii(){return Ii=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ii.apply(this,arguments)}var kn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(kn||(kn={}));const ff="popstate";function $2(e){e===void 0&&(e={});function t(r,o){let{pathname:s,search:i,hash:l}=r.location;return ru("",{pathname:s,search:i,hash:l},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:hg(o)}return U2(t,n,null,e)}function Ke(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function mg(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function B2(){return Math.random().toString(36).substr(2,8)}function pf(e,t){return{usr:e.state,key:e.key,idx:t}}function ru(e,t,n,r){return n===void 0&&(n=null),Ii({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?dl(t):t,{state:n,key:t&&t.key||r||B2()})}function hg(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function dl(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function U2(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:s=!1}=r,i=o.history,l=kn.Pop,a=null,u=d();u==null&&(u=0,i.replaceState(Ii({},i.state,{idx:u}),""));function d(){return(i.state||{idx:null}).idx}function p(){l=kn.Pop;let x=d(),g=x==null?null:x-u;u=x,a&&a({action:l,location:y.location,delta:g})}function h(x,g){l=kn.Push;let m=ru(y.location,x,g);u=d()+1;let v=pf(m,u),S=y.createHref(m);try{i.pushState(v,"",S)}catch(E){if(E instanceof DOMException&&E.name==="DataCloneError")throw E;o.location.assign(S)}s&&a&&a({action:l,location:y.location,delta:1})}function f(x,g){l=kn.Replace;let m=ru(y.location,x,g);u=d();let v=pf(m,u),S=y.createHref(m);i.replaceState(v,"",S),s&&a&&a({action:l,location:y.location,delta:0})}function b(x){let g=o.location.origin!=="null"?o.location.origin:o.location.href,m=typeof x=="string"?x:hg(x);return m=m.replace(/ $/,"%20"),Ke(g,"No window.location.(origin|href) available to create URL for href: "+m),new URL(m,g)}let y={get action(){return l},get location(){return e(o,i)},listen(x){if(a)throw new Error("A history only accepts one active listener");return o.addEventListener(ff,p),a=x,()=>{o.removeEventListener(ff,p),a=null}},createHref(x){return t(o,x)},createURL:b,encodeLocation(x){let g=b(x);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:h,replace:f,go(x){return i.go(x)}};return y}var mf;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(mf||(mf={}));function V2(e,t,n){return n===void 0&&(n="/"),W2(e,t,n)}function W2(e,t,n,r){let o=typeof t=="string"?dl(t):t,s=yg(o.pathname||"/",n);if(s==null)return null;let i=gg(e);H2(i);let l=null;for(let a=0;l==null&&a<i.length;++a){let u=rb(s);l=eb(i[a],u)}return l}function gg(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(s,i,l)=>{let a={relativePath:l===void 0?s.path||"":l,caseSensitive:s.caseSensitive===!0,childrenIndex:i,route:s};a.relativePath.startsWith("/")&&(Ke(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=Ur([r,a.relativePath]),d=n.concat(a);s.children&&s.children.length>0&&(Ke(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),gg(s.children,t,d,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:Z2(u,s.index),routesMeta:d})};return e.forEach((s,i)=>{var l;if(s.path===""||!((l=s.path)!=null&&l.includes("?")))o(s,i);else for(let a of vg(s.path))o(s,i,a)}),t}function vg(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return o?[s,""]:[s];let i=vg(r.join("/")),l=[];return l.push(...i.map(a=>a===""?s:[s,a].join("/"))),o&&l.push(...i),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function H2(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:J2(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Q2=/^:[\w-]+$/,K2=3,Y2=2,G2=1,q2=10,X2=-2,hf=e=>e==="*";function Z2(e,t){let n=e.split("/"),r=n.length;return n.some(hf)&&(r+=X2),t&&(r+=Y2),n.filter(o=>!hf(o)).reduce((o,s)=>o+(Q2.test(s)?K2:s===""?G2:q2),r)}function J2(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function eb(e,t,n){let{routesMeta:r}=e,o={},s="/",i=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,d=s==="/"?t:t.slice(s.length)||"/",p=tb({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},d),h=a.route;if(!p)return null;Object.assign(o,p.params),i.push({params:o,pathname:Ur([s,p.pathname]),pathnameBase:ob(Ur([s,p.pathnameBase])),route:h}),p.pathnameBase!=="/"&&(s=Ur([s,p.pathnameBase]))}return i}function tb(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=nb(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let s=o[0],i=s.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce((u,d,p)=>{let{paramName:h,isOptional:f}=d;if(h==="*"){let y=l[p]||"";i=s.slice(0,s.length-y.length).replace(/(.)\/+$/,"$1")}const b=l[p];return f&&!b?u[h]=void 0:u[h]=(b||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:i,pattern:e}}function nb(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),mg(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function rb(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return mg(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function yg(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}const Ur=e=>e.join("/").replace(/\/\/+/g,"/"),ob=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");function sb(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const xg=["post","put","patch","delete"];new Set(xg);const ib=["get",...xg];new Set(ib);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Di(){return Di=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Di.apply(this,arguments)}const lb=w.createContext(null),ab=w.createContext(null),wg=w.createContext(null),fl=w.createContext(null),pl=w.createContext({outlet:null,matches:[],isDataRoute:!1}),bg=w.createContext(null);function yc(){return w.useContext(fl)!=null}function Sg(){return yc()||Ke(!1),w.useContext(fl).location}function ub(e,t){return cb(e,t)}function cb(e,t,n,r){yc()||Ke(!1);let{navigator:o}=w.useContext(wg),{matches:s}=w.useContext(pl),i=s[s.length-1],l=i?i.params:{};i&&i.pathname;let a=i?i.pathnameBase:"/";i&&i.route;let u=Sg(),d;if(t){var p;let x=typeof t=="string"?dl(t):t;a==="/"||(p=x.pathname)!=null&&p.startsWith(a)||Ke(!1),d=x}else d=u;let h=d.pathname||"/",f=h;if(a!=="/"){let x=a.replace(/^\//,"").split("/");f="/"+h.replace(/^\//,"").split("/").slice(x.length).join("/")}let b=V2(e,{pathname:f}),y=hb(b&&b.map(x=>Object.assign({},x,{params:Object.assign({},l,x.params),pathname:Ur([a,o.encodeLocation?o.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?a:Ur([a,o.encodeLocation?o.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),s,n,r);return t&&y?w.createElement(fl.Provider,{value:{location:Di({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:kn.Pop}},y):y}function db(){let e=xb(),t=sb(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},t),n?w.createElement("pre",{style:o},n):null,null)}const fb=w.createElement(db,null);class pb extends w.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?w.createElement(pl.Provider,{value:this.props.routeContext},w.createElement(bg.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function mb(e){let{routeContext:t,match:n,children:r}=e,o=w.useContext(lb);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),w.createElement(pl.Provider,{value:t},r)}function hb(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,l=(o=n)==null?void 0:o.errors;if(l!=null){let d=i.findIndex(p=>p.route.id&&(l==null?void 0:l[p.route.id])!==void 0);d>=0||Ke(!1),i=i.slice(0,Math.min(i.length,d+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<i.length;d++){let p=i[d];if((p.route.HydrateFallback||p.route.hydrateFallbackElement)&&(u=d),p.route.id){let{loaderData:h,errors:f}=n,b=p.route.loader&&h[p.route.id]===void 0&&(!f||f[p.route.id]===void 0);if(p.route.lazy||b){a=!0,u>=0?i=i.slice(0,u+1):i=[i[0]];break}}}return i.reduceRight((d,p,h)=>{let f,b=!1,y=null,x=null;n&&(f=l&&p.route.id?l[p.route.id]:void 0,y=p.route.errorElement||fb,a&&(u<0&&h===0?(wb("route-fallback"),b=!0,x=null):u===h&&(b=!0,x=p.route.hydrateFallbackElement||null)));let g=t.concat(i.slice(0,h+1)),m=()=>{let v;return f?v=y:b?v=x:p.route.Component?v=w.createElement(p.route.Component,null):p.route.element?v=p.route.element:v=d,w.createElement(mb,{match:p,routeContext:{outlet:d,matches:g,isDataRoute:n!=null},children:v})};return n&&(p.route.ErrorBoundary||p.route.errorElement||h===0)?w.createElement(pb,{location:n.location,revalidation:n.revalidation,component:y,error:f,children:m(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):m()},null)}var Eg=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Eg||{});function gb(e){let t=w.useContext(ab);return t||Ke(!1),t}function vb(e){let t=w.useContext(pl);return t||Ke(!1),t}function yb(e){let t=vb(),n=t.matches[t.matches.length-1];return n.route.id||Ke(!1),n.route.id}function xb(){var e;let t=w.useContext(bg),n=gb(Eg.UseRouteError),r=yb();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}const gf={};function wb(e,t,n){gf[e]||(gf[e]=!0)}function bb(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function ou(e){Ke(!1)}function Sb(e){let{basename:t="/",children:n=null,location:r,navigationType:o=kn.Pop,navigator:s,static:i=!1,future:l}=e;yc()&&Ke(!1);let a=t.replace(/^\/*/,"/"),u=w.useMemo(()=>({basename:a,navigator:s,static:i,future:Di({v7_relativeSplatPath:!1},l)}),[a,l,s,i]);typeof r=="string"&&(r=dl(r));let{pathname:d="/",search:p="",hash:h="",state:f=null,key:b="default"}=r,y=w.useMemo(()=>{let x=yg(d,a);return x==null?null:{location:{pathname:x,search:p,hash:h,state:f,key:b},navigationType:o}},[a,d,p,h,f,b,o]);return y==null?null:w.createElement(wg.Provider,{value:u},w.createElement(fl.Provider,{children:n,value:y}))}function Eb(e){let{children:t,location:n}=e;return ub(su(t),n)}new Promise(()=>{});function su(e,t){t===void 0&&(t=[]);let n=[];return w.Children.forEach(e,(r,o)=>{if(!w.isValidElement(r))return;let s=[...t,o];if(r.type===w.Fragment){n.push.apply(n,su(r.props.children,s));return}r.type!==ou&&Ke(!1),!r.props.index||!r.props.children||Ke(!1);let i={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=su(r.props.children,s)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Cb="6";try{window.__reactRouterVersion=Cb}catch{}const Nb="startTransition",vf=Ff[Nb];function kb(e){let{basename:t,children:n,future:r,window:o}=e,s=w.useRef();s.current==null&&(s.current=$2({window:o,v5Compat:!0}));let i=s.current,[l,a]=w.useState({action:i.action,location:i.location}),{v7_startTransition:u}=r||{},d=w.useCallback(p=>{u&&vf?vf(()=>a(p)):a(p)},[a,u]);return w.useLayoutEffect(()=>i.listen(d),[i,d]),w.useEffect(()=>bb(r),[r]),w.createElement(Sb,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:i,future:r})}var yf;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(yf||(yf={}));var xf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(xf||(xf={}));const Pb=sc("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",hero:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-glow hover:shadow-glow transition-all duration-300",cta:"bg-accent text-accent-foreground hover:bg-accent/90 font-semibold tracking-wide",outline_glow:"border border-foreground/20 bg-background/10 backdrop-blur-sm text-foreground hover:bg-foreground/10 hover:border-foreground/40"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Pn=w.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>{const i=r?f0:"button";return c.jsx(i,{className:_e(Pb({variant:t,size:n,className:e})),ref:s,...o})});Pn.displayName="Button";const xc=w.forwardRef(({className:e,type:t,...n},r)=>c.jsx("input",{type:t,className:_e("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));xc.displayName="Input";const jb=sc("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Gt({className:e,variant:t,...n}){return c.jsx("div",{className:_e(jb({variant:t}),e),...n})}const Tb=()=>{const[e,t]=w.useState(""),[n,r]=w.useState(!1),o=s=>{s.preventDefault(),e&&(r(!0),console.log("Waitlist signup:",e))};return c.jsxs("div",{className:"min-h-screen bg-hero-gradient relative overflow-hidden",children:[c.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[c.jsx("div",{className:"absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse"}),c.jsx("div",{className:"absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse delay-1000"}),c.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-128 h-128 bg-tech-accent/5 rounded-full blur-3xl animate-pulse delay-2000"})]}),c.jsxs("div",{className:"relative z-10 container mx-auto px-4 py-20",children:[c.jsx("div",{className:"text-center mb-8",children:c.jsxs(Gt,{variant:"secondary",className:"px-4 py-2 text-sm font-medium bg-primary/20 text-primary border-primary/30",children:[c.jsx(ji,{className:"w-4 h-4 mr-2"}),"Industrial Process Intelligence"]})}),c.jsxs("div",{className:"text-center max-w-6xl mx-auto mb-12",children:[c.jsxs("div",{className:"flex items-center justify-center mb-6",children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg"}),c.jsx("div",{className:"w-3 h-8 bg-gradient-to-br from-accent to-tech-accent rounded-sm"}),c.jsx("div",{className:"w-6 h-8 bg-gradient-to-br from-tech-accent to-primary rounded-lg"})]}),c.jsx("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold text-foreground ml-4",children:"Jentrix AI"})]}),c.jsxs("h2",{className:"text-2xl md:text-3xl lg:text-4xl font-semibold text-foreground mb-6",children:["Transforming Manufacturing with",c.jsxs("span",{className:"text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text",children:[" ","Custom Solutions"]})]}),c.jsx("p",{className:"text-lg md:text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed",children:"We craft powerful AI solutions that solve complex problems and drive innovation across industries. Our tailored approach brings intelligence to your business challenges with advanced correlation discovery and time series forecasting for industrial optimization."})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-4 mb-16",children:[c.jsxs(Pn,{variant:"hero",size:"lg",className:"px-8 py-3",children:[c.jsx(Ri,{className:"mr-2 h-5 w-5"}),"Explore Solutions"]}),c.jsx(Pn,{variant:"outline_glow",size:"lg",className:"px-8 py-3",children:"Contact Us"})]}),c.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 mb-20",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-3xl md:text-4xl font-bold text-primary mb-2",children:"295K+"}),c.jsx("div",{className:"text-sm text-muted-foreground",children:"Records Processed"})]}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-3xl md:text-4xl font-bold text-accent mb-2",children:"99.5%"}),c.jsx("div",{className:"text-sm text-muted-foreground",children:"Accuracy Rate"})]}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-3xl md:text-4xl font-bold text-tech-accent mb-2",children:"25%"}),c.jsx("div",{className:"text-sm text-muted-foreground",children:"Scrap Reduction"})]}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-3xl md:text-4xl font-bold text-success-green mb-2",children:"$1M+"}),c.jsx("div",{className:"text-sm text-muted-foreground",children:"Annual Savings"})]})]}),c.jsxs("div",{className:"max-w-2xl mx-auto bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-card-custom",children:[c.jsxs("div",{className:"text-center mb-6",children:[c.jsx("h3",{className:"text-2xl font-bold text-foreground mb-2",children:"Join the Waitlist"}),c.jsx("p",{className:"text-muted-foreground",children:"Be the first to access our revolutionary industrial AI platform"})]}),n?c.jsxs("div",{className:"text-center py-8",children:[c.jsx(Ti,{className:"w-16 h-16 text-success-green mx-auto mb-4"}),c.jsx("h4",{className:"text-xl font-semibold text-foreground mb-2",children:"Thank you for joining!"}),c.jsx("p",{className:"text-muted-foreground",children:"We'll keep you updated on our progress and notify you when Jentrix AI is ready for early access."})]}):c.jsxs("form",{onSubmit:o,className:"space-y-4",children:[c.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[c.jsx(xc,{type:"email",placeholder:"Enter your email address",value:e,onChange:s=>t(s.target.value),className:"flex-1 bg-background/50 border-border/50 focus:border-primary",required:!0}),c.jsxs(Pn,{type:"submit",variant:"cta",className:"sm:w-auto",children:["Join Waitlist",c.jsx(xh,{className:"ml-2 h-4 w-4"})]})]}),c.jsx("p",{className:"text-xs text-muted-foreground text-center",children:"No spam. We'll only send updates about our platform launch."})]})]}),c.jsxs("div",{className:"text-center mt-20",children:[c.jsx("h3",{className:"text-3xl font-bold text-foreground mb-4",children:"Core Focus Areas"}),c.jsx("p",{className:"text-muted-foreground mb-12 max-w-2xl mx-auto",children:"Our expertise drives innovation in these key domains"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[c.jsxs("div",{className:"bg-card/30 backdrop-blur-sm border border-border/30 rounded-xl p-6 hover:bg-card/50 transition-all duration-300",children:[c.jsx(ji,{className:"w-12 h-12 text-primary mx-auto mb-4"}),c.jsx("h4",{className:"text-lg font-semibold text-foreground mb-2",children:"AI-Powered Analytics"}),c.jsx("p",{className:"text-sm text-muted-foreground",children:"Advanced correlation discovery and predictive modeling"})]}),c.jsxs("div",{className:"bg-card/30 backdrop-blur-sm border border-border/30 rounded-xl p-6 hover:bg-card/50 transition-all duration-300",children:[c.jsx(wh,{className:"w-12 h-12 text-accent mx-auto mb-4"}),c.jsx("h4",{className:"text-lg font-semibold text-foreground mb-2",children:"Process Optimization"}),c.jsx("p",{className:"text-sm text-muted-foreground",children:"Real-time monitoring and quality improvement solutions"})]}),c.jsxs("div",{className:"bg-card/30 backdrop-blur-sm border border-border/30 rounded-xl p-6 hover:bg-card/50 transition-all duration-300",children:[c.jsx(Ri,{className:"w-12 h-12 text-tech-accent mx-auto mb-4"}),c.jsx("h4",{className:"text-lg font-semibold text-foreground mb-2",children:"Enterprise Integration"}),c.jsx("p",{className:"text-sm text-muted-foreground",children:"Seamless deployment in existing manufacturing systems"})]})]})]})]})]})},Re=w.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:_e("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Re.displayName="Card";const st=w.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:_e("flex flex-col space-y-1.5 p-6",e),...t}));st.displayName="CardHeader";const it=w.forwardRef(({className:e,...t},n)=>c.jsx("h3",{ref:n,className:_e("text-2xl font-semibold leading-none tracking-tight",e),...t}));it.displayName="CardTitle";const zi=w.forwardRef(({className:e,...t},n)=>c.jsx("p",{ref:n,className:_e("text-sm text-muted-foreground",e),...t}));zi.displayName="CardDescription";const Ae=w.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:_e("p-6 pt-0",e),...t}));Ae.displayName="CardContent";const Rb=w.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:_e("flex items-center p-6 pt-0",e),...t}));Rb.displayName="CardFooter";const Ab=()=>{const e=[{icon:vx,title:"Quality Issues",problems:["15-25% scrap rates in complex processes","Inconsistent product quality across production runs","Reactive quality control instead of predictive","High rework and customer complaints impact profitability"],color:"text-warning-orange"},{icon:ax,title:"Operational Inefficiencies",problems:["Unplanned downtime costs $50K/hour for major facilities","Sub-optimal process parameters reduce efficiency","Energy waste from inefficient operations","Limited visibility into root causes of problems"],color:"text-destructive"},{icon:Vd,title:"Data Complexity",problems:["Mixed-frequency sensor data difficult to correlate","Temporal alignment challenges across systems","Correlation vs causation confusion leads to wrong decisions","Siloed data sources prevent holistic analysis"],color:"text-tech-accent"}],t=[{icon:ji,title:"Smart Correlation Engine",description:"Multi-method analysis with intelligent method selection and bootstrap stability validation",features:["Pearson, Spearman, Kendall analysis","Bootstrap stability validation","Mixed-frequency data integration"],color:"text-primary"},{icon:mx,title:"AI Agent Framework",description:"Natural language manufacturing queries powered by Claude and Vertex AI",features:["Natural language queries","LLM-powered insights","Automated visualization generation"],color:"text-accent"},{icon:wh,title:"Advanced Forecasting",description:"PatchTST transformer architecture for manufacturing time series predictions",features:["Multi-horizon predictions","Uncertainty quantification","Manufacturing domain intelligence"],color:"text-success-green"},{icon:Ri,title:"Real-Time Processing",description:"Sub-second correlation calculations with live monitoring and alerting",features:["Sub-second calculations","Live monitoring & alerting","Event-driven analysis"],color:"text-tech-accent"}],n=[{name:"FreqMixFormer",description:"Frequency-aware transformer for mixed-resolution industrial data",impact:"Handles complex multi-frequency manufacturing data"},{name:"MF-TFCCA",description:"Mixed-Frequency Time-Frequency Canonical Correlation Analysis",impact:"Discovers hidden correlations across time scales"},{name:"PatchTST",description:"State-of-the-art transformer for manufacturing time series",impact:"Superior forecasting accuracy for production planning"},{name:"LLM Integration",description:"Claude & Vertex AI for manufacturing expertise",impact:"Natural language interface with domain knowledge"}],r=[{icon:gx,title:"Natural Language Queries",example:"Compare correlations between speed and thickness using optimal method"},{icon:Vd,title:"Professional Visualizations",example:"Multi-method heatmaps showing correlation strength across methods"},{icon:Ya,title:"22+ Specialized Tools",example:"Bootstrap sampling for statistical confidence and robustness testing"}],o=[{metric:"Manufacturing Records Processed",value:"295,373+",significance:"Enterprise-scale validation"},{metric:"Timestamp Alignment Accuracy",value:"99.5%",significance:"Critical for temporal correlation"},{metric:"Comprehensive Tests",value:"73 (100% Success)",significance:"Production-ready reliability"},{metric:"Internal Precision",value:"8 decimal places",significance:"High-precision manufacturing analysis"}];return c.jsx("div",{className:"py-20 bg-background",children:c.jsxs("div",{className:"container mx-auto px-4",children:[c.jsxs("section",{className:"mb-20",children:[c.jsxs("div",{className:"text-center mb-12",children:[c.jsx(Gt,{variant:"destructive",className:"mb-4",children:"🚨 The Manufacturing Challenge"}),c.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Critical Issues Facing Modern Manufacturing"}),c.jsx("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Complex manufacturing processes face significant challenges that impact quality, efficiency, and profitability"})]}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:e.map((s,i)=>c.jsxs(Re,{className:"bg-card/50 border-border/50 hover:bg-card/70 transition-all duration-300",children:[c.jsx(st,{children:c.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[c.jsx(s.icon,{className:`w-8 h-8 ${s.color}`}),c.jsx(it,{className:"text-lg",children:s.title})]})}),c.jsx(Ae,{children:c.jsx("ul",{className:"space-y-2",children:s.problems.map((l,a)=>c.jsxs("li",{className:"text-sm text-muted-foreground flex items-start gap-2",children:[c.jsx("div",{className:"w-1.5 h-1.5 bg-muted-foreground rounded-full mt-2 flex-shrink-0"}),l]},a))})})]},i))})]}),c.jsxs("section",{className:"mb-20",children:[c.jsxs("div",{className:"text-center mb-12",children:[c.jsx(Gt,{variant:"default",className:"mb-4 bg-primary/20 text-primary border-primary/30",children:"💡 The Jentrix AI Solution"}),c.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Intelligent Multi-Method Correlation Discovery + Advanced Time Series Forecasting"})]}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:t.map((s,i)=>c.jsxs(Re,{className:"bg-card/50 border-border/50 hover:bg-card/70 transition-all duration-300",children:[c.jsxs(st,{children:[c.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[c.jsx(s.icon,{className:`w-8 h-8 ${s.color}`}),c.jsx(it,{className:"text-xl",children:s.title})]}),c.jsx(zi,{className:"text-base",children:s.description})]}),c.jsx(Ae,{children:c.jsx("ul",{className:"space-y-2",children:s.features.map((l,a)=>c.jsxs("li",{className:"text-sm text-muted-foreground flex items-center gap-2",children:[c.jsx("div",{className:"w-2 h-2 bg-primary rounded-full"}),l]},a))})})]},i))})]}),c.jsxs("section",{className:"mb-20",children:[c.jsxs("div",{className:"text-center mb-12",children:[c.jsx(Gt,{variant:"secondary",className:"mb-4",children:"🔬 Cutting-Edge Technology Stack"}),c.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Core AI/ML Technologies"})]}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:n.map((s,i)=>c.jsxs(Re,{className:"bg-card/50 border-border/50 hover:bg-card/70 transition-all duration-300",children:[c.jsxs(st,{children:[c.jsx(it,{className:"text-lg text-primary",children:s.name}),c.jsx(zi,{children:s.description})]}),c.jsx(Ae,{children:c.jsxs("div",{className:"flex items-start gap-2",children:[c.jsx(Ya,{className:"w-4 h-4 text-success-green mt-0.5"}),c.jsx("span",{className:"text-sm text-muted-foreground",children:s.impact})]})})]},i))})]}),c.jsxs("section",{className:"mb-20",children:[c.jsx("div",{className:"text-center mb-12",children:c.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Product Capabilities"})}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:r.map((s,i)=>c.jsxs(Re,{className:"bg-card/50 border-border/50 hover:bg-card/70 transition-all duration-300",children:[c.jsx(st,{children:c.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[c.jsx(s.icon,{className:"w-6 h-6 text-accent"}),c.jsx(it,{className:"text-lg",children:s.title})]})}),c.jsx(Ae,{children:c.jsx("div",{className:"bg-muted/30 p-3 rounded-lg",children:c.jsx("code",{className:"text-sm text-foreground",children:s.example})})})]},i))})]}),c.jsxs("section",{children:[c.jsxs("div",{className:"text-center mb-12",children:[c.jsx(Gt,{variant:"default",className:"mb-4 bg-success-green/20 text-success-green border-success-green/30",children:"📈 Proven Results & Validation"}),c.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Performance Metrics"})]}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:o.map((s,i)=>c.jsx(Re,{className:"bg-card/50 border-border/50",children:c.jsxs(Ae,{className:"p-6",children:[c.jsxs("div",{className:"flex items-center justify-between mb-2",children:[c.jsx("span",{className:"text-sm font-medium text-muted-foreground",children:s.metric}),c.jsx("span",{className:"text-2xl font-bold text-primary",children:s.value})]}),c.jsx("p",{className:"text-sm text-muted-foreground",children:s.significance})]})},i))}),c.jsxs("div",{className:"mt-12 grid grid-cols-2 md:grid-cols-5 gap-6",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-2xl font-bold text-success-green mb-1",children:"15-25%"}),c.jsx("div",{className:"text-xs text-muted-foreground",children:"Scrap Rate Reduction"})]}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-2xl font-bold text-primary mb-1",children:"5-10%"}),c.jsx("div",{className:"text-xs text-muted-foreground",children:"Yield Improvement"})]}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-2xl font-bold text-accent mb-1",children:"20-30%"}),c.jsx("div",{className:"text-xs text-muted-foreground",children:"Quality Consistency"})]}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-2xl font-bold text-tech-accent mb-1",children:"10-20%"}),c.jsx("div",{className:"text-xs text-muted-foreground",children:"Downtime Reduction"})]}),c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-2xl font-bold text-warning-orange mb-1",children:"$500K-$1M"}),c.jsx("div",{className:"text-xs text-muted-foreground",children:"Annual Savings"})]})]})]})]})})},Mb=()=>{const e=[{stream:"Annual Licensing",value:"$100K-$500K per facility",description:"Core platform access"},{stream:"Implementation Services",value:"$50K-$200K",description:"Custom deployment and integration"},{stream:"Premium Support",value:"$25K-$100K",description:"Dedicated support and maintenance"},{stream:"Custom Development",value:"$200K-$1M",description:"Industry-specific features and integrations"}],t=[{year:"Year 1",revenue:"$2M ARR",customers:"10 enterprise customers"},{year:"Year 2",revenue:"$8M ARR",customers:"30 enterprise customers"},{year:"Year 3",revenue:"$20M ARR",customers:"60 enterprise customers"},{year:"Year 4",revenue:"$40M ARR",customers:"100 enterprise customers"}],n=[{industry:"Chemical Processing",description:"Complex multi-stage processes with mixed-frequency data",icon:ux},{industry:"Food & Beverage",description:"Quality control optimization and regulatory compliance",icon:Ti},{industry:"Pharmaceuticals",description:"Regulatory compliance + strict quality requirements",icon:Ya},{industry:"Steel & Metals",description:"Process optimization and energy efficiency",icon:Ri}],r=[{metric:"$300B+",label:"Industrial IoT Market",description:"Manufacturing as largest segment"},{metric:"15%",label:"Annual Growth Rate",description:"Manufacturing analytics sector"},{metric:"$3.8B",label:"AI in Manufacturing",description:"Market size by 2025"},{metric:"70%",label:"Planning AI Adoption",description:"Manufacturers in next 3 years"}],o=["First industrial application of FreqMixFormer to manufacturing","Novel LLM agent integration with industrial process monitoring","Advanced mixed-frequency correlation analysis for manufacturing","Production-validated AI agents with real manufacturing data","Deep domain knowledge integrated into AI models","Sub-second response times for correlation calculations"];return c.jsx("div",{className:"py-20 bg-muted/30",children:c.jsxs("div",{className:"container mx-auto px-4",children:[c.jsxs("section",{className:"mb-20",children:[c.jsxs("div",{className:"text-center mb-12",children:[c.jsx(Gt,{variant:"default",className:"mb-4 bg-warning-orange/20 text-warning-orange border-warning-orange/30",children:"📊 Massive Market Opportunity"}),c.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Market Size & Growth"})]}),c.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12",children:r.map((s,i)=>c.jsx(Re,{className:"bg-card/50 border-border/50 text-center",children:c.jsxs(Ae,{className:"p-6",children:[c.jsx("div",{className:"text-3xl font-bold text-primary mb-2",children:s.metric}),c.jsx("div",{className:"text-sm font-semibold text-foreground mb-1",children:s.label}),c.jsx("div",{className:"text-xs text-muted-foreground",children:s.description})]})},i))}),c.jsxs("div",{className:"mb-12",children:[c.jsx("h3",{className:"text-2xl font-bold text-foreground mb-8 text-center",children:"Target Industries"}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:n.map((s,i)=>c.jsxs(Re,{className:"bg-card/50 border-border/50 hover:bg-card/70 transition-all duration-300",children:[c.jsxs(st,{className:"text-center",children:[c.jsx(s.icon,{className:"w-12 h-12 text-accent mx-auto mb-3"}),c.jsx(it,{className:"text-lg",children:s.industry})]}),c.jsx(Ae,{children:c.jsx("p",{className:"text-sm text-muted-foreground text-center",children:s.description})})]},i))})]})]}),c.jsxs("section",{className:"mb-20",children:[c.jsxs("div",{className:"text-center mb-12",children:[c.jsx(Gt,{variant:"default",className:"mb-4 bg-success-green/20 text-success-green border-success-green/30",children:"💰 Business Model & Revenue"}),c.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Enterprise SaaS Model"})]}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:e.map((s,i)=>c.jsxs(Re,{className:"bg-card/50 border-border/50",children:[c.jsxs(st,{children:[c.jsx(it,{className:"text-lg text-primary",children:s.stream}),c.jsx(zi,{className:"text-xl font-bold text-foreground",children:s.value})]}),c.jsx(Ae,{children:c.jsx("p",{className:"text-sm text-muted-foreground",children:s.description})})]},i))}),c.jsxs("div",{className:"mb-12",children:[c.jsx("h3",{className:"text-2xl font-bold text-foreground mb-8 text-center",children:"Revenue Projections"}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:t.map((s,i)=>c.jsx(Re,{className:"bg-card/50 border-border/50 text-center hover:bg-card/70 transition-all duration-300",children:c.jsxs(Ae,{className:"p-6",children:[c.jsx("div",{className:"text-lg font-semibold text-accent mb-2",children:s.year}),c.jsx("div",{className:"text-2xl font-bold text-primary mb-2",children:s.revenue}),c.jsx("div",{className:"text-sm text-muted-foreground",children:s.customers})]})},i))})]}),c.jsxs(Re,{className:"bg-gradient-to-r from-primary/10 to-accent/10 border-primary/20",children:[c.jsx(st,{className:"text-center",children:c.jsx(it,{className:"text-2xl text-foreground",children:"Customer ROI"})}),c.jsx(Ae,{children:c.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 text-center",children:[c.jsxs("div",{children:[c.jsx("div",{className:"text-2xl font-bold text-success-green mb-1",children:"6-12 months"}),c.jsx("div",{className:"text-sm text-muted-foreground",children:"Payback Period"})]}),c.jsxs("div",{children:[c.jsx("div",{className:"text-2xl font-bold text-primary mb-1",children:"300-500%"}),c.jsx("div",{className:"text-sm text-muted-foreground",children:"ROI in first year"})]}),c.jsxs("div",{children:[c.jsx("div",{className:"text-2xl font-bold text-accent mb-1",children:"20-40%"}),c.jsx("div",{className:"text-sm text-muted-foreground",children:"Quality Cost Reduction"})]}),c.jsxs("div",{children:[c.jsx("div",{className:"text-2xl font-bold text-tech-accent mb-1",children:"15-25%"}),c.jsx("div",{className:"text-sm text-muted-foreground",children:"Maintenance Cost Reduction"})]})]})})]})]}),c.jsxs("section",{className:"mb-20",children:[c.jsxs("div",{className:"text-center mb-12",children:[c.jsx(Gt,{variant:"default",className:"mb-4 bg-tech-accent/20 text-tech-accent border-tech-accent/30",children:"🚀 Competitive Advantages"}),c.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Why Choose Jentrix AI"})]}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:o.map((s,i)=>c.jsx(Re,{className:"bg-card/50 border-border/50 hover:bg-card/70 transition-all duration-300",children:c.jsx(Ae,{className:"p-6",children:c.jsxs("div",{className:"flex items-start gap-3",children:[c.jsx(Ti,{className:"w-6 h-6 text-success-green mt-0.5 flex-shrink-0"}),c.jsx("p",{className:"text-sm text-muted-foreground",children:s})]})})},i))})]}),c.jsxs("section",{children:[c.jsx("div",{className:"text-center mb-12",children:c.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Expansion Strategy"})}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[c.jsxs(Re,{className:"bg-card/50 border-border/50 hover:bg-card/70 transition-all duration-300",children:[c.jsxs(st,{children:[c.jsx("div",{className:"w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center mb-3",children:c.jsx("span",{className:"text-primary font-bold",children:"1"})}),c.jsx(it,{className:"text-lg",children:"Proven Use Cases"})]}),c.jsx(Ae,{children:c.jsx("p",{className:"text-sm text-muted-foreground",children:"Start with validated fiber cement manufacturing results"})})]}),c.jsxs(Re,{className:"bg-card/50 border-border/50 hover:bg-card/70 transition-all duration-300",children:[c.jsxs(st,{children:[c.jsx("div",{className:"w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center mb-3",children:c.jsx("span",{className:"text-accent font-bold",children:"2"})}),c.jsx(it,{className:"text-lg",children:"Horizontal Expansion"})]}),c.jsx(Ae,{children:c.jsx("p",{className:"text-sm text-muted-foreground",children:"Similar process industries with comparable challenges"})})]}),c.jsxs(Re,{className:"bg-card/50 border-border/50 hover:bg-card/70 transition-all duration-300",children:[c.jsxs(st,{children:[c.jsx("div",{className:"w-8 h-8 bg-tech-accent/20 rounded-full flex items-center justify-center mb-3",children:c.jsx("span",{className:"text-tech-accent font-bold",children:"3"})}),c.jsx(it,{className:"text-lg",children:"Geographic Scaling"})]}),c.jsx(Ae,{children:c.jsx("p",{className:"text-sm text-muted-foreground",children:"International markets expansion"})})]}),c.jsxs(Re,{className:"bg-card/50 border-border/50 hover:bg-card/70 transition-all duration-300",children:[c.jsxs(st,{children:[c.jsx("div",{className:"w-8 h-8 bg-success-green/20 rounded-full flex items-center justify-center mb-3",children:c.jsx("span",{className:"text-success-green font-bold",children:"4"})}),c.jsx(it,{className:"text-lg",children:"Platform Ecosystem"})]}),c.jsx(Ae,{children:c.jsx("p",{className:"text-sm text-muted-foreground",children:"Partner integrations and extensible architecture"})})]})]})]})]})})},Ob=()=>{const[e,t]=w.useState(""),[n,r]=w.useState(!1),o=s=>{s.preventDefault(),e&&(r(!0),t(""))};return c.jsx("footer",{className:"bg-card border-t border-border",children:c.jsxs("div",{className:"container mx-auto px-4 py-16",children:[c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:[c.jsxs("div",{className:"lg:col-span-2",children:[c.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-primary to-accent rounded-md"}),c.jsx("div",{className:"w-2 h-6 bg-gradient-to-br from-accent to-tech-accent rounded-sm"}),c.jsx("div",{className:"w-4 h-6 bg-gradient-to-br from-tech-accent to-primary rounded-md"})]}),c.jsx("h3",{className:"text-2xl font-bold text-foreground",children:"Jentrix AI"})]}),c.jsx("p",{className:"text-muted-foreground mb-6 max-w-md",children:"Transforming manufacturing with AI-powered process monitoring and prediction. Advanced correlation discovery and time series forecasting for industrial optimization."}),c.jsx("div",{className:"flex items-center space-x-4",children:c.jsxs(Gt,{variant:"secondary",className:"bg-primary/20 text-primary border-primary/30",children:[c.jsx(ji,{className:"w-3 h-3 mr-1"}),"Industrial Process Intelligence"]})})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-semibold text-foreground mb-4",children:"Product"}),c.jsxs("ul",{className:"space-y-3",children:[c.jsx("li",{children:c.jsx("a",{href:"#features",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Features"})}),c.jsx("li",{children:c.jsx("a",{href:"#technology",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Technology Stack"})}),c.jsx("li",{children:c.jsx("a",{href:"#capabilities",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Capabilities"})}),c.jsx("li",{children:c.jsx("a",{href:"#results",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Proven Results"})})]})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-semibold text-foreground mb-4",children:"Company"}),c.jsxs("ul",{className:"space-y-3",children:[c.jsx("li",{children:c.jsx("a",{href:"#about",className:"text-muted-foreground hover:text-foreground transition-colors",children:"About Us"})}),c.jsx("li",{children:c.jsx("a",{href:"#business-model",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Business Model"})}),c.jsx("li",{children:c.jsx("a",{href:"#industries",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Target Industries"})}),c.jsx("li",{children:c.jsx("a",{href:"#contact",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Contact"})})]})]})]}),c.jsx("div",{className:"bg-muted/30 rounded-2xl p-8 mb-12",children:c.jsxs("div",{className:"max-w-2xl mx-auto text-center",children:[c.jsx("h3",{className:"text-2xl font-bold text-foreground mb-2",children:"Stay Updated on Our Progress"}),c.jsx("p",{className:"text-muted-foreground mb-6",children:"Get the latest updates on Jentrix AI development, industry insights, and early access opportunities"}),n?c.jsxs("div",{className:"flex items-center justify-center gap-2 text-success-green",children:[c.jsx(Ti,{className:"w-5 h-5"}),c.jsx("span",{children:"Thank you for subscribing!"})]}):c.jsxs("form",{onSubmit:o,className:"flex flex-col sm:flex-row gap-3 max-w-md mx-auto",children:[c.jsx(xc,{type:"email",placeholder:"Your email address",value:e,onChange:s=>t(s.target.value),className:"flex-1",required:!0}),c.jsxs(Pn,{type:"submit",variant:"cta",children:[c.jsx(fx,{className:"mr-2 h-4 w-4"}),"Subscribe"]})]})]})}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[c.jsxs("div",{className:"text-center",children:[c.jsx(lx,{className:"w-8 h-8 text-primary mx-auto mb-3"}),c.jsx("h4",{className:"font-semibold text-foreground mb-2",children:"Enterprise Sales"}),c.jsx("p",{className:"text-muted-foreground text-sm",children:"Ready to transform your manufacturing processes?"}),c.jsxs(Pn,{variant:"outline",size:"sm",className:"mt-3",children:["Schedule Demo",c.jsx(xh,{className:"ml-2 h-3 w-3"})]})]}),c.jsxs("div",{className:"text-center",children:[c.jsx(hx,{className:"w-8 h-8 text-accent mx-auto mb-3"}),c.jsx("h4",{className:"font-semibold text-foreground mb-2",children:"Support"}),c.jsx("p",{className:"text-muted-foreground text-sm",children:"Implementation and technical support"}),c.jsx(Pn,{variant:"outline",size:"sm",className:"mt-3",children:"Contact Support"})]}),c.jsxs("div",{className:"text-center",children:[c.jsx(px,{className:"w-8 h-8 text-tech-accent mx-auto mb-3"}),c.jsx("h4",{className:"font-semibold text-foreground mb-2",children:"Partnership"}),c.jsx("p",{className:"text-muted-foreground text-sm",children:"Integration and reseller opportunities"}),c.jsx(Pn,{variant:"outline",size:"sm",className:"mt-3",children:"Partner With Us"})]})]}),c.jsx("div",{className:"border-t border-border pt-8",children:c.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4",children:[c.jsx("div",{className:"text-sm text-muted-foreground",children:"© 2024 Jentrix AI. All rights reserved. | Industrial Process Intelligence Platform"}),c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground transition-colors",children:c.jsx(dx,{className:"w-5 h-5"})}),c.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground transition-colors",children:c.jsx(yx,{className:"w-5 h-5"})}),c.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground transition-colors",children:c.jsx(cx,{className:"w-5 h-5"})})]})]})}),c.jsx("div",{className:"mt-12 text-center",children:c.jsxs("div",{className:"inline-flex items-center gap-2 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full px-6 py-3 border border-primary/30",children:[c.jsx("div",{className:"w-2 h-2 bg-success-green rounded-full animate-pulse"}),c.jsx("span",{className:"text-sm font-medium text-foreground",children:"Early Access Program Now Open"})]})})]})})},_b=()=>c.jsxs("div",{className:"min-h-screen bg-background",children:[c.jsx(Tb,{}),c.jsx(Ab,{}),c.jsx(Mb,{}),c.jsx(Ob,{})]}),Lb=()=>{const e=Sg();return w.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),c.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:c.jsxs("div",{className:"text-center",children:[c.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),c.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),c.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},Ib=new D2;function Db(){return c.jsx(F2,{client:Ib,children:c.jsxs(p2,{children:[c.jsx(tw,{}),c.jsx(jw,{}),c.jsx(kb,{children:c.jsxs(Eb,{children:[c.jsx(ou,{path:"/",element:c.jsx(_b,{})}),c.jsx(ou,{path:"*",element:c.jsx(Lb,{})})]})})]})})}Vm(document.getElementById("root")).render(c.jsx(Db,{}));

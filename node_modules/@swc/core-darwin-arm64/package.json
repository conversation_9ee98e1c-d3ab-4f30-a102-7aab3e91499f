{"name": "@swc/core-darwin-arm64", "version": "1.12.11", "os": ["darwin"], "cpu": ["arm64"], "main": "swc.darwin-arm64.node", "files": ["swc.darwin-arm64.node", "swc"], "description": "Super-fast alternative for babel", "keywords": ["swc", "swcpack", "babel", "typescript", "rust", "webpack", "tsc"], "author": "강동윤 <<EMAIL>>", "homepage": "https://swc.rs", "license": "Apache-2.0 AND MIT", "engines": {"node": ">=10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/swc-project/swc.git"}, "bugs": {"url": "https://github.com/swc-project/swc/issues"}}